import { MedusaService } from "@camped-ai/framework/utils";
import { DAL } from "@camped-ai/framework/types";
import {
  BasePriceRule,
  SeasonalPriceRule,
  ChannelPriceOverride,
  OccupancyConfig,
  MealPlan,
} from "./models";

type InjectedDependencies = {
  baseRepository: DAL.RepositoryService
}

export interface GetHotelPricingParams {
  hotel_id: string;
  currency_code?: string;
  room_config_id?: string;
  meal_plan_id?: string;
  occupancy_config_id?: string;
  check_in_date?: string;
  check_out_date?: string;
  include_seasonal?: boolean;
  store_context?: boolean;
}

export interface HotelPricingResponse {
  base_price_rules: any[];
  seasonal_price_rules: any[];
  meal_plans: any[];
  occupancy_configs: any[];
  room_configs?: any[];
  currency_code: string;
  store_context?: boolean;
}

/**
 * Service for managing hotel pricing
 */
class HotelPricingService extends MedusaService({
  BasePriceRule,
  SeasonalPriceRule,
  ChannelPriceOverride,
  OccupancyConfig,
  MealPlan,
}) {
  protected baseRepository_: DAL.RepositoryService

  constructor({ baseRepository }: InjectedDependencies) {
    super(...arguments)
    this.baseRepository_ = baseRepository
  }

  /**
   * Get comprehensive hotel pricing data for store/public use
   */
  async getHotelPricing(params: GetHotelPricingParams): Promise<HotelPricingResponse> {
    const {
      hotel_id,
      currency_code = "USD",
      room_config_id,
      meal_plan_id,
      occupancy_config_id,
      check_in_date,
      check_out_date,
      include_seasonal = true,
      store_context = false,
    } = params;

    console.log(`[HotelPricingService] Getting pricing for hotel: ${hotel_id}, currency: ${currency_code}`);

    // Get base price rules
    const basePriceFilters: any = { hotel_id };
    if (currency_code) basePriceFilters.currency_code = currency_code;
    if (room_config_id) basePriceFilters.room_config_id = room_config_id;
    if (meal_plan_id) basePriceFilters.meal_plan_id = meal_plan_id;
    if (occupancy_config_id) basePriceFilters.occupancy_type_id = occupancy_config_id;

    const basePriceRules = await this.listBasePriceRules(basePriceFilters);

    // Get meal plans for this hotel
    const mealPlans = await this.listMealPlans({ hotel_id });

    // Get occupancy configurations for this hotel
    const occupancyConfigs = await this.listOccupancyConfigs({ hotel_id });

    // Get seasonal price rules if requested
    let seasonalPriceRules: any[] = [];
    if (include_seasonal && basePriceRules.length > 0) {
      // Get seasonal rules for the base price rules we found
      const basePriceRuleIds = basePriceRules.map(rule => rule.id);

      const seasonalFilters: any = {
        base_price_rule_id: basePriceRuleIds
      };

      if (currency_code) seasonalFilters.currency_code = currency_code;

      // Filter by date range if provided
      if (check_in_date && check_out_date) {
        // Add date filtering logic here if needed
        // For now, get all seasonal rules and let the client filter
      }

      seasonalPriceRules = await this.listSeasonalPriceRules(seasonalFilters);
    }

    const response: HotelPricingResponse = {
      base_price_rules: basePriceRules,
      seasonal_price_rules: seasonalPriceRules,
      meal_plans: mealPlans,
      occupancy_configs: occupancyConfigs,
      currency_code,
    };

    if (store_context) {
      response.store_context = true;
      // Add any store-specific filtering or formatting here
    }

    console.log(`[HotelPricingService] Returning pricing data with ${basePriceRules.length} base rules, ${seasonalPriceRules.length} seasonal rules`);

    return response;
  }
}

export default HotelPricingService;