import { MedusaError, MedusaErrorTypes } from "@camped-ai/utils";
import { EntityManager } from "typeorm";
import { Relationship } from "./models/customer-traveller";

// Service operates against the existing model() API entity "customer_traveller" table.
// This version uses TypeORM QueryBuilder (no raw SQL strings) to follow Medusa standards.

export type CreateTravellerInput = {
  customer_id: string;
  cart_id?: string | null;
  first_name: string;
  last_name: string;
  date_of_birth: string; // ISO string
  gender?: "male" | "female" | "other";
  relationship?: Relationship;
  is_primary?: boolean;
};

export type UpdateTravellerInput = Partial<Omit<CreateTravellerInput, "customer_id">> & {
  id: string;
};

const TABLE = "customer_traveller";

export default class CustomerTravellerService {
  private readonly manager: EntityManager;

  static readonly DEFAULT_MAX_TRAVELLERS = 10; // 1 primary + 9 additional

  constructor(deps: { manager: EntityManager }) {
    this.manager = deps.manager;
  }

  private qb(manager: EntityManager) {
    // Use QueryBuilder against a plain table name via createQueryBuilder().from()
    return manager.createQueryBuilder().from(TABLE, TABLE);
  }

  private async listAllByCustomerTx(tx: EntityManager, customer_id: string) {
    return await this.qb(tx)
      .select("*")
      .where(`"${TABLE}"."customer_id" = :customer_id`, { customer_id })
      .andWhere(`"${TABLE}"."deleted_at" IS NULL`)
      .orderBy(`"${TABLE}"."created_at"`, "ASC")
      .getRawMany();
  }

  async listByCustomer(customer_id: string) {
    return await this.qb(this.manager)
      .select("*")
      .where(`"${TABLE}"."customer_id" = :customer_id`, { customer_id })
      .andWhere(`"${TABLE}"."deleted_at" IS NULL`)
      .orderBy(`"${TABLE}"."created_at"`, "ASC")
      .getRawMany();
  }

  // Medusa-standard: service method to list travellers scoped to a specific cart
  async listByCart(cart_id: string) {
    return await this.qb(this.manager)
      .select("*")
      .where(`"${TABLE}"."cart_id" = :cart_id`, { cart_id })
      .andWhere(`"${TABLE}"."deleted_at" IS NULL`)
      .orderBy(`"${TABLE}"."created_at"`, "ASC")
      .getRawMany();
  }

  async getByIdForCustomer(id: string, customer_id: string) {
    const row = await this.qb(this.manager)
      .select("*")
      .where(`"${TABLE}"."id" = :id`, { id })
      .andWhere(`"${TABLE}"."customer_id" = :customer_id`, { customer_id })
      .andWhere(`"${TABLE}"."deleted_at" IS NULL`)
      .getRawOne();

    if (!row) {
      throw new MedusaError(MedusaErrorTypes.NOT_FOUND, "Traveller profile not found");
    }
    return row;
  }

  private async assertConstraints(input: CreateTravellerInput, tx: EntityManager) {
    const profiles = await this.listAllByCustomerTx(tx, input.customer_id);

    if (profiles.length >= CustomerTravellerService.DEFAULT_MAX_TRAVELLERS) {
      throw new MedusaError(MedusaErrorTypes.NOT_ALLOWED, "Maximum number of traveller profiles reached");
    }

    const is_primary = !!input.is_primary;

    if (is_primary) {
      const existingPrimary = profiles.find((p: any) => p.is_primary);
      if (existingPrimary) {
        throw new MedusaError(MedusaErrorTypes.NOT_ALLOWED, "Primary traveller already exists for this customer");
      }
    } else {
      // For additional travellers, relationship is required
      if (!input.relationship) {
        throw new MedusaError(MedusaErrorTypes.INVALID_DATA, "Relationship is required for additional travellers");
      }
    }
  }

  async create(input: CreateTravellerInput) {
    return await this.manager.transaction(async (tx) => {
      await this.assertConstraints(input, tx);

      // Generate id via database function using QueryBuilder insert with returning
      const insertRes = await tx
        .createQueryBuilder()
        .insert()
        .into(TABLE)
        .values({
          // TypeORM QueryBuilder with plain table uses raw expressions via () => '...'
          id: () => `concat('cust_trav_', replace(gen_random_uuid()::text, '-', ''))`,
          customer_id: input.customer_id,
          first_name: input.first_name,
          last_name: input.last_name,
          gender: input.gender ?? null,
          date_of_birth: new Date(input.date_of_birth) as any,
          relationship: (input.relationship ?? null) as any,
          is_primary: input.is_primary ?? false,
          cart_id: input.cart_id ?? null,
          created_at: () => "now()",
          updated_at: () => "now()",
        } as any)
        .returning("*")
        .execute();

      return insertRes.raw?.[0] ?? insertRes.generatedMaps?.[0];
    });
  }

  async update(customer_id: string, id: string, input: UpdateTravellerInput) {
    return await this.manager.transaction(async (tx) => {
      const existing = await this.qb(tx)
        .select("*")
        .where(`"${TABLE}"."id" = :id`, { id })
        .andWhere(`"${TABLE}"."customer_id" = :customer_id`, { customer_id })
        .andWhere(`"${TABLE}"."deleted_at" IS NULL`)
        .getRawOne();

      if (!existing) {
        throw new MedusaError(MedusaErrorTypes.NOT_FOUND, "Traveller profile not found");
      }

      // If making this primary, ensure uniqueness
      if (input.is_primary === true && !existing.is_primary) {
        const primaryCount = await this.qb(tx)
          .select("COUNT(1)", "c")
          .where(`"${TABLE}"."customer_id" = :customer_id`, { customer_id })
          .andWhere(`"${TABLE}"."is_primary" = true`)
          .andWhere(`"${TABLE}"."deleted_at" IS NULL`)
          .getRawOne();
        const hasPrimary = Number(primaryCount?.c ?? 0) > 0;
        if (hasPrimary) {
          throw new MedusaError(MedusaErrorTypes.NOT_ALLOWED, "Primary traveller already exists for this customer");
        }
      }

      // For non-primary additional travellers, ensure relationship exists
      const next_is_primary = input.is_primary ?? existing.is_primary;
      const next_relationship = input.relationship ?? existing.relationship;

      if (!next_is_primary && !next_relationship) {
        throw new MedusaError(MedusaErrorTypes.INVALID_DATA, "Relationship is required for additional travellers");
      }

      const updateRes = await tx
        .createQueryBuilder()
        .update(TABLE)
        .set({
          first_name: input.first_name ?? existing.first_name,
          last_name: input.last_name ?? existing.last_name,
          gender: (input.gender ?? existing.gender) as any,
          date_of_birth: input.date_of_birth ? (new Date(input.date_of_birth) as any) : existing.date_of_birth,
          relationship: (next_relationship ?? null) as any,
          is_primary: next_is_primary,
          cart_id: input.cart_id ?? existing.cart_id,
          updated_at: () => "now()",
        } as any)
        .where(`"id" = :id`, { id })
        .andWhere(`"customer_id" = :customer_id`, { customer_id })
        .andWhere(`"deleted_at" IS NULL`)
        .returning("*")
        .execute();

      return updateRes.raw?.[0] ?? updateRes.generatedMaps?.[0];
    });
  }

  async delete(customer_id: string, id: string): Promise<void> {
    await this.manager.transaction(async (tx) => {
      await tx
        .createQueryBuilder()
        .update(TABLE)
        .set({ deleted_at: () => "now()", updated_at: () => "now()" } as any)
        .where(`"id" = :id`, { id })
        .andWhere(`"customer_id" = :customer_id`, { customer_id })
        .andWhere(`"deleted_at" IS NULL`)
        .execute();
    });
  }
}
