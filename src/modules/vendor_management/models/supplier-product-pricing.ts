import { model } from "@camped-ai/framework/utils";
import { SupplierProduct } from "./supplier-product";

export const SupplierProductPricing = model
  .define("supplier_product_pricing", {
    id: model.id({ prefix: "sprice" }).primaryKey(),
    pricing_type: model.enum([
      "fixed", 
      "tiered", 
      "seasonal", 
      "bulk_discount", 
      "time_based"
    ]),
    base_price: model.number(), // Base price in smallest currency unit (e.g., cents)
    currency_code: model.text().default("USD"),
    
    // Tiered pricing rules
    tier_rules: model.json().nullable(), // [{ min_qty: 10, price: 9.50 }]
    
    // Seasonal pricing rules
    seasonal_rules: model.json().nullable(), // [{ season: "peak", multiplier: 1.2 }]
    
    // Time-based pricing rules
    time_rules: model.json().nullable(), // [{ day_of_week: "weekend", multiplier: 1.1 }]
    
    // Bulk discount rules
    bulk_discount_rules: model.json().nullable(), // [{ min_qty: 100, discount_percent: 10 }]
    
    // Validity period
    valid_from: model.dateTime().nullable(),
    valid_until: model.dateTime().nullable(),
    is_active: model.boolean().default(true),
    
    // Relationships
    product: model.belongsTo(() => SupplierProduct, {
      foreignKey: "product_id",
    }),
  })
  .indexes([
    {
      name: "IDX_supplier_product_pricing_product_id",
      on: ["product_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_product_pricing_type",
      on: ["pricing_type"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_product_pricing_active",
      on: ["is_active"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_product_pricing_validity",
      on: ["valid_from", "valid_until"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
