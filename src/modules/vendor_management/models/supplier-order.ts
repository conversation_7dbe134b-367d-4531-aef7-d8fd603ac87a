import { model } from "@camped-ai/framework/utils";
import { Supplier } from "./supplier";
import { SupplierOrderItem } from "./supplier-order-item";

export const SupplierOrder = model
  .define("supplier_order", {
    id: model.id({ prefix: "sorder" }).primaryKey(),
    order_number: model.text(),
    order_name: model.text().nullable(), // Custom order name
    status: model
      .enum(["pending", "confirmed", "in_progress", "completed", "cancelled"])
      .default("pending"),
    order_type: model.enum(["product", "service", "mixed"]),

    // Financial Information (all amounts in smallest currency unit - e.g., cents)
    subtotal: model.number(), // Subtotal in smallest currency unit
    tax_amount: model.number().default(0), // Tax amount in smallest currency unit
    total_amount: model.number(), // Total amount in smallest currency unit
    currency_code: model.text().default("USD"),

    // Delivery Information
    requested_delivery_date: model.dateTime().nullable(),
    actual_delivery_date: model.dateTime().nullable(),
    delivery_address: model.text().nullable(),

    // Order Details
    notes: model.text().nullable(),
    internal_notes: model.text().nullable(),

    // Customer Information
    customer_name: model.text().nullable(),
    customer_email: model.text().nullable(),
    customer_phone: model.text().nullable(),

    // Hotel/Booking Reference
    hotel_id: model.text().nullable(),
    booking_id: model.text().nullable(),

    // Metadata for additional information
    metadata: model.json().nullable(),

    // Relationships
    supplier: model.belongsTo(() => Supplier, {
      foreignKey: "supplier_id",
    }),
    items: model.hasMany(() => SupplierOrderItem, {
      mappedBy: "order",
    }),
  })
  .indexes([
    {
      name: "IDX_supplier_order_supplier_id",
      on: ["supplier_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_order_number",
      on: ["order_number"],
      unique: true,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_order_status",
      on: ["status"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_order_status_date",
      on: ["status", "created_at"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_order_hotel_booking",
      on: ["hotel_id", "booking_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_order_delivery_date",
      on: ["requested_delivery_date"],
      unique: false,
      where: "deleted_at IS NULL",
    },
  ]);
