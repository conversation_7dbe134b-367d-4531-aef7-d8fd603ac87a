import { model } from "@camped-ai/framework/utils";
import { SupplierContract } from "./supplier-contract";

export const SupplierContractPayment = model
  .define("supplier_contract_payment", {
    id: model.id({ prefix: "scpay" }).primaryKey(),
    payment_number: model.text(),
    amount: model.number(), // Amount in smallest currency unit (e.g., cents for USD)
    currency_code: model.text().default("USD"),
    due_date: model.dateTime(),
    paid_date: model.dateTime().nullable(),
    status: model.enum([
      "pending", 
      "paid", 
      "overdue", 
      "cancelled"
    ]).default("pending"),
    payment_method: model.text().nullable(),
    reference_number: model.text().nullable(),
    notes: model.text().nullable(),
    
    // Relationships
    contract: model.belongsTo(() => SupplierContract, {
      foreignKey: "contract_id",
    }),
  })
  .indexes([
    {
      name: "IDX_supplier_contract_payment_contract_id",
      on: ["contract_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_contract_payment_status",
      on: ["status"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_contract_payment_due_date",
      on: ["due_date"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_contract_payment_paid_date",
      on: ["paid_date"],
      unique: false,
      where: "deleted_at IS NULL AND paid_date IS NOT NULL",
    },
  ]);
