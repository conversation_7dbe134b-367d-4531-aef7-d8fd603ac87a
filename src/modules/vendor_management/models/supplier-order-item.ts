import { model } from "@camped-ai/framework/utils";
import { SupplierOrder } from "./supplier-order";

export const SupplierOrderItem = model
  .define("supplier_order_item", {
    id: model.id({ prefix: "sorderitem" }).primaryKey(),
    item_type: model.enum(["product", "service"]),
    item_id: model.text(), // References either supplier_product.id or supplier_service.id
    item_name: model.text(),
    item_description: model.text().nullable(),
    
    // Quantity and Pricing (prices in smallest currency unit - e.g., cents)
    quantity: model.number(),
    unit_price: model.number(), // Unit price in smallest currency unit
    total_price: model.number(), // Total price in smallest currency unit
    
    // Service-specific fields
    service_date: model.dateTime().nullable(),
    service_duration_minutes: model.number().nullable(),
    
    // Product-specific fields
    product_sku: model.text().nullable(),
    
    // Item specifications or customizations
    specifications: model.json().nullable(),
    
    // Status
    status: model.enum([
      "pending", 
      "confirmed", 
      "in_progress", 
      "completed", 
      "cancelled"
    ]).default("pending"),
    
    // Notes
    notes: model.text().nullable(),
    
    // Relationships
    order: model.belongsTo(() => SupplierOrder, {
      foreignKey: "order_id",
    }),
  })
  .indexes([
    {
      name: "IDX_supplier_order_item_order_id",
      on: ["order_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_order_item_type_id",
      on: ["item_type", "item_id"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_order_item_status",
      on: ["status"],
      unique: false,
      where: "deleted_at IS NULL",
    },
    {
      name: "IDX_supplier_order_item_service_date",
      on: ["service_date"],
      unique: false,
      where: "deleted_at IS NULL AND service_date IS NOT NULL",
    },
  ]);
