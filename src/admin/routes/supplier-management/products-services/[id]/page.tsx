import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { ArrowLeft } from "@camped-ai/icons";
import { Edit, Package, Trash, Plus } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Badge,
  Toaster,
  Prompt,
  toast,
} from "@camped-ai/ui";
import { useNavigate, useParams } from "react-router-dom";
import { useState, lazy, Suspense } from "react";
import PermissionBasedSidebarHider from "../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../hooks/use-rbac";
import {
  useProductService,
  useDeleteProductService,
} from "../../../../hooks/supplier-products-services/use-products-services";


// Dynamically import page client for better performance
const ProductServiceDetailsLayout = lazy(() => import("./page-client"));

const ProductServiceDetailPage = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { hasPermission } = useRbac();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  // Use real API hooks
  const { data: productServiceData, isLoading, error } = useProductService(id!);
  const deleteProductService = useDeleteProductService();

  const productService = productServiceData?.product_service;

  // Handle edit navigation
  const handleEdit = () => {
    if (productService) {
      navigate(`/supplier-management/products-services/${productService.id}/edit`);
    }
  };

  // Handle error state
  if (error) {
    return (
      <>
        <PermissionBasedSidebarHider />
        <Container className="max-w-4xl mx-auto">
          <div className="flex items-center gap-4 mb-6">
            <Button
              variant="secondary"
              size="small"
              onClick={() => navigate("/supplier-management/products-services")}
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <Heading level="h2">Product/Service Not Found</Heading>
              <Text className="text-ui-fg-subtle">
                The requested product or service could not be found.
              </Text>
            </div>
          </div>
        </Container>
        <Toaster />
      </>
    );
  }

  // Handle loading state
  if (isLoading) {
    return (
      <>
        <PermissionBasedSidebarHider />

        {/* Header with back button and actions skeleton */}
        <div className="flex flex-row items-center justify-between mb-2">
          <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
          <div className="flex items-center gap-2">
            <div className="h-8 w-16 bg-gray-200 rounded animate-pulse" />
            <div className="h-8 w-20 bg-gray-200 rounded animate-pulse" />
          </div>
        </div>

        <Container>
          {/* Product/Service header skeleton */}
          <div className="flex flex-row items-center text-wrap mb-5 ">
            <div className="flex-shrink-0 w-12 h-12 bg-gray-200 rounded-lg animate-pulse" />
            <div className="h-6 w-48 bg-gray-200 rounded animate-pulse ml-3" />
          </div>

          {/* Tab navigation skeleton */}
          <div className="my-6">
            <div className="flex space-x-8">
              <div className="h-4 w-16 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
            </div>
          </div>

          {/* Tab content skeleton */}
          <div className="space-y-6">
            <div className="h-32 bg-gray-200 rounded animate-pulse" />
            <div className="h-32 bg-gray-200 rounded animate-pulse" />
            <div className="h-32 bg-gray-200 rounded animate-pulse" />
            <div className="h-32 bg-gray-200 rounded animate-pulse" />
          </div>
        </Container>

        <Toaster />
      </>
    );
  }

  console.log("productService", productService);

  if (!productService) {
    return null;
  }

  const handleDelete = async () => {
    if (!productService) return;

    // Dismiss any existing toasts and show loading toast
    toast.dismiss();
    const loadingToast = toast.loading("Deleting product/service...");

    try {
      await deleteProductService.mutateAsync(productService.id);
      toast.dismiss(loadingToast);
      toast.success("Product/Service deleted successfully");
      setIsDeleteModalOpen(false);
      navigate("/supplier-management/products-services");
    } catch (error) {
      toast.dismiss(loadingToast);
      toast.error("Failed to delete product/service");
      console.error("Error deleting product/service:", error);
      setIsDeleteModalOpen(false);
    }
  };

  const handleCancelDelete = () => {
    setIsDeleteModalOpen(false);
  };

  const handleDeleteClick = () => {
    setIsDeleteModalOpen(true);
  };

  const handleCreateSupplierOffering = () => {
    if (!productService) return;

    // Navigate to create supplier offering page with pre-populated data
    const params = new URLSearchParams();
    params.append("product_service_id", productService.id);
    if (productService.category_id) {
      params.append("category_id", productService.category_id);
    }

    navigate(`/supplier-management/supplier-offerings/create?${params.toString()}`);
  };

  return (
    <>
      <PermissionBasedSidebarHider />

      <Container className="divide-y p-0 mb-1">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center gap-4">
            <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-r from-blue-500 to-purple-500">
              <Package className="h-6 w-6 text-white" />
            </div>
            <div>
              <Heading level="h1" className="text-2xl">
                {productService?.name}
              </Heading>

            </div>
            <div className="flex items-center gap-2 mt-1">
              <Badge color={productService?.type === "Product" ? "blue" : "purple"} size="xsmall">
                {productService?.type}
              </Badge>
              <Badge color={productService?.status === "active" ? "green" : "grey"} size="xsmall">
                {productService?.status?.charAt(0).toUpperCase() + productService?.status?.slice(1)}
              </Badge>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {hasPermission("supplier_management:create") && (
              <Button
                variant="primary"
                onClick={handleCreateSupplierOffering}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Supplier Offering
              </Button>
            )}
            {hasPermission("supplier_management:edit") && (
              <Button
                variant="secondary"
                onClick={handleEdit}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            )}
            {hasPermission("supplier_management:delete") && (
              <Button variant="danger" onClick={handleDeleteClick}>
                <Trash className="h-4 w-4" />
                Delete
              </Button>
            )}
          </div>
        </div>
      </Container>

      <Suspense
        fallback={
          <Container className="p-6">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-3 text-sm text-gray-600">Loading product details...</span>
            </div>
          </Container>
        }
      >
        <ProductServiceDetailsLayout
          productService={productService}
        />
      </Suspense>

      {/* Delete Confirmation Modal */}
      <Prompt open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Product/Service</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete "{productService?.name}"? This
              action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={handleCancelDelete}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action onClick={handleDelete}>Delete</Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      <Toaster />
    </>
  );
};

export const config = defineRouteConfig({
  label: "Product/Service Details",
});

export default ProductServiceDetailPage;
