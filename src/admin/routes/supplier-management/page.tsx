import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Buildings,
  PlusMini,
  MagnifyingGlass,
  Adjustments,
} from "@camped-ai/icons";
import {
  Edit,
  Upload as UploadIcon,
  Download,
  TrendingUp,
  Package,
  Handshake,
  FileText,
  ShoppingCart,
} from "lucide-react";
import OutlineButton from "../../components/shared/OutlineButton";
import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  FocusModal,
  Toaster,
  toast,
  Drawer,
  Table,
  Badge,
} from "@camped-ai/ui";
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../components/rbac/RoleGuard";
import {
  useSuppliers,
  Supplier,
} from "../../hooks/vendor-management/use-suppliers";
import { useRbac } from "../../hooks/use-rbac";
import { useDashboardMetrics } from "../../hooks/supplier-management/use-dashboard-metrics";

// Use Supplier interface from the hook

const SupplierManagementPage = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [vendors, setVendors] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedVendors, setSelectedVendors] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  // Use the supplier management hook
  const { data: suppliersData, isLoading, error } = useSuppliers();

  // Use dashboard metrics hook
  const {
    data: metrics,
    isLoading: metricsLoading,
    error: metricsError,
  } = useDashboardMetrics();

  useEffect(() => {
    if (suppliersData?.suppliers) {
      setVendors(suppliersData.suppliers);
      setLoading(false);
    }
  }, [suppliersData, isLoading]);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    // Filter vendors based on search term
    if (suppliersData?.suppliers) {
      const filtered = suppliersData.suppliers.filter(
        (vendor) =>
          vendor.name.toLowerCase().includes(term.toLowerCase()) ||
          vendor.primary_contact_name
            .toLowerCase()
            .includes(term.toLowerCase()) ||
          vendor.handle.toLowerCase().includes(term.toLowerCase())
      );
      setVendors(filtered);
    }
  };

  const handleVendorSelect = (vendorId: string) => {
    setSelectedVendors((prev) =>
      prev.includes(vendorId)
        ? prev.filter((id) => id !== vendorId)
        : [...prev, vendorId]
    );
  };

  const handleSelectAll = () => {
    if (selectedVendors.length === vendors.length) {
      setSelectedVendors([]);
    } else {
      setSelectedVendors(vendors.map((v) => v.id));
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "green";
      case "inactive":
        return "red";
      case "pending":
        return "orange";
      default:
        return "grey";
    }
  };

  const getVerificationBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "verified":
        return "green";
      case "pending":
        return "orange";
      case "rejected":
        return "red";
      default:
        return "grey";
    }
  };

  // Quick navigation cards for the dashboard
  const allNavigationCards = [
    {
      title: "Supplier Directory",
      description: "Manage all suppliers and their information",
      href: "/supplier-management/suppliers",
      icon: "👥",
      count: vendors.length,
      permission: "supplier_management:view",
    },
    {
      title: "Products & Services",
      description: "Manage supplier products and services",
      href: "/supplier-management/products-services",
      icon: "📦",
      count: 0, // TODO: Get actual count
      permission: "supplier_management:view",
    },
    {
      title: "Supplier Offerings",
      description: "Manage which suppliers offer which products/services",
      href: "/supplier-management/supplier-offerings",
      icon: "🤝",
      count: 0, // TODO: Get actual count
      permission: "supplier_management:view",
    },
    {
      title: "Orders",
      description: "Track and manage supplier orders",
      href: "/supplier-management/orders",
      icon: "📋",
      count: 0, // TODO: Get actual count
      permission: "supplier_orders:view",
    },
    {
      title: "Contracts",
      description: "Manage supplier contracts and agreements",
      href: "/supplier-management/contracts",
      icon: "📄",
      count: 0, // TODO: Get actual count
      permission: "supplier_management:view",
    },
    {
      title: "Availability",
      description: "View and manage supplier offering availability",
      href: "/supplier-management/availability",
      icon: "📅",
      count: 0, // TODO: Get actual count
      permission: "supplier_management:view",
    },
  ];

  // Filter navigation cards based on permissions
  const navigationCards = allNavigationCards.filter((card) =>
    hasPermission(card.permission)
  );

  // KPI Cards for dashboard metrics
  const kpiCards = [
    {
      title: "Active Suppliers",
      value: metrics?.active_suppliers_count || 0,
      icon: TrendingUp,
      color: "text-green-600",
      bgColor: "bg-green-50",
      href: "/supplier-management/suppliers?status=Active",
      isLoading: metricsLoading,
      isPlaceholder: false,
    },
    {
      title: "Active Products & Services",
      value: metrics?.products_services_count || 0,
      icon: Package,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      href: "/supplier-management/products-services?status=active",
      isLoading: metricsLoading,
      isPlaceholder: false,
    },
    {
      title: "Active Offerings",
      value: metrics?.active_offerings_count || 0,
      icon: Handshake,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      href: "/supplier-management/supplier-offerings?status=active",
      isLoading: metricsLoading,
      isPlaceholder: false,
    },
    {
      title: "Pending Orders",
      value: metrics?.pending_orders_count || 0,
      icon: ShoppingCart,
      color: "text-gray-400",
      bgColor: "bg-gray-50",
      href: "/supplier-management/orders?status=pending",
      isLoading: metricsLoading,
      isPlaceholder: false,
    },
  ];

  // Core Functions Cards
  const coreFunctionCards = [
    {
      title: "Supplier Directory",
      description: "Navigate to list of all suppliers",
      icon: "👥",
      count: metrics?.total_suppliers_count || 0,
      href: "/supplier-management/suppliers",
      isPlaceholder: false,
    },
    {
      title: "Products & Services",
      description: "Navigate to master product/service definitions",
      icon: "📦",
      count: metrics?.products_services_count || 0,
      href: "/supplier-management/products-services",
      isPlaceholder: false,
    },
    {
      title: "Supplier Offerings",
      description: "Navigate to supplier-wise pricing & availability",
      icon: "🤝",
      count: metrics?.total_offerings_count || 0,
      href: "/supplier-management/supplier-offerings",
      isPlaceholder: false,
    },
    {
      title: "Orders",
      description: "Navigate to supplier sales orders",
      icon: "📋",
      count: 0,
      href: "/supplier-management/orders",
      isPlaceholder: false,
    },
  ];

  // Configuration cards for global lookups
  const allConfigurationCards = [
    {
      title: "Categories",
      description: "Manage product and service categories",
      href: "/supplier-management/config/categories",
      icon: "🏷️",
      permission: "supplier_management:view",
    },
    {
      title: "Unit Types",
      description: "Manage billing and measurement units",
      href: "/supplier-management/config/unit-types",
      icon: "📏",
      permission: "supplier_management:view",
    },
    {
      title: "Exchange Rates",
      description: "Manage currency exchange rates for transactions",
      href: "/supplier-management/config/exchange-rates",
      icon: "💱",
      permission: "supplier_management:view",
    },
  ];

  // Filter configuration cards based on permissions
  const configurationCards = allConfigurationCards.filter((card) =>
    hasPermission(card.permission)
  );

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="supplier_management:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view supplier management.
              </Text>
            </div>
          </Container>
        }
      >
        <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Supplier Management</Heading>
          </div>
          {/* <div className="flex items-center gap-x-2">
            {hasPermission("supplier_management:create") && (
              <Button
                size="small"
                onClick={() =>
                  navigate("/supplier-management/suppliers/create")
                }
              >
                <PlusMini />
                Add Supplier
              </Button>
            )}
          </div> */}
        </div>

        {/* KPI Cards Section */}
        <div className="px-6 py-6">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {kpiCards.map((card) => (
              <div
                key={card.title}
                className={`p-4 rounded-lg border transition-all duration-200 ${
                  card.isPlaceholder
                    ? "bg-gray-50 border-gray-200 opacity-60 cursor-not-allowed"
                    : "bg-white border-gray-200 hover:shadow-md hover:border-gray-300 cursor-pointer"
                }`}
                onClick={() => !card.isPlaceholder && navigate(card.href)}
              >
                <div className="flex items-center justify-between">
                  <div className={`p-2 rounded-lg ${card.bgColor}`}>
                    <card.icon className={`h-5 w-5 ${card.color}`} />
                  </div>
                  <div className="flex items-center gap-2">
                    {!card.isPlaceholder && (
                      <div className="text-xl font-bold text-gray-900">
                        {card.isLoading ? "..." : card.value.toLocaleString()}
                      </div>
                    )}
                    {card.isPlaceholder && (
                      <Badge color="grey" className="text-xs">
                        Coming Soon
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="mt-3">
                  <div className="text-sm text-gray-600">{card.title}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Core Functions Section */}
        <div className="px-6 py-6">
          <div className="mb-6">
            <Heading level="h3">Core Functions</Heading>
            <Text className="text-ui-fg-subtle">
              Primary supplier management operations and data
            </Text>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {coreFunctionCards.map((card) => (
              <div
                key={card.title}
                className={`p-5 border rounded-lg transition-all duration-200 ${
                  card.isPlaceholder
                    ? "bg-gray-50 border-gray-200 opacity-60 cursor-not-allowed"
                    : "bg-white border-gray-200 hover:shadow-lg hover:border-blue-300 cursor-pointer group"
                }`}
                onClick={() => !card.isPlaceholder && navigate(card.href)}
              >
                <div className="flex items-start gap-4">
                  <div
                    className={`p-2 rounded-lg ${
                      card.isPlaceholder
                        ? "bg-gray-100"
                        : "bg-blue-50 group-hover:bg-blue-100"
                    } transition-colors`}
                  >
                    <span className="text-2xl opacity-50">{card.icon}</span>
                  </div>
                  <div className="flex-1">
                    <div
                      className={`font-semibold ${
                        card.isPlaceholder
                          ? "text-gray-500"
                          : "text-gray-900 group-hover:text-blue-700"
                      } transition-colors flex items-center gap-2`}
                    >
                      {card.title}
                      {card.isPlaceholder && (
                        <Badge color="grey" className="text-xs">
                          Coming Soon
                        </Badge>
                      )}
                    </div>
                    <div
                      className={`text-sm mt-1 leading-relaxed ${
                        card.isPlaceholder ? "text-gray-400" : "text-gray-600"
                      }`}
                    >
                      {card.description}
                    </div>
                    {!card.isPlaceholder && (
                      <div className="mt-3 flex items-center justify-between">
                        <span className="text-xs text-blue-600 font-medium group-hover:text-blue-700">
                          View all →
                        </span>
                        {/* <span className="text-lg font-bold text-gray-900">
                          {metricsLoading ? "..." : card.count.toLocaleString()}
                        </span> */}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Configuration Section - Enhanced for Sub-Menu Functionality */}
        {configurationCards.length > 0 && (
          <div className="px-6 py-6 border-t bg-gray-50">
            <div className="mb-6">
              <Heading level="h3" className="flex items-center gap-2">
                <Adjustments className="h-5 w-5 text-gray-600" />
                Configuration & Settings
              </Heading>
              <Text className="text-ui-fg-subtle">
                Manage global lookups, categories, and system settings for
                supplier management
              </Text>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {configurationCards.map((card) => (
                <div
                  key={card.title}
                  className="p-5 bg-white border rounded-lg hover:shadow-lg hover:border-blue-300 transition-all duration-200 cursor-pointer group"
                  onClick={() => navigate(card.href)}
                >
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-blue-50 rounded-lg group-hover:bg-blue-100 transition-colors">
                      <span className="text-2xl">{card.icon}</span>
                    </div>
                    <div className="flex-1">
                      <div className="font-semibold text-gray-900 group-hover:text-blue-700 transition-colors">
                        {card.title}
                      </div>
                      <div className="text-sm text-gray-600 mt-1 leading-relaxed">
                        {card.description}
                      </div>
                      <div className="mt-3">
                        <span className="text-xs text-blue-600 font-medium group-hover:text-blue-700">
                          Manage {card.title.toLowerCase()} →
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        </Container>
        <Toaster />
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Supplier Management",
  icon: Buildings,
});

export default SupplierManagementPage;
