import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Text } from "@camped-ai/ui";
import { useMemo, lazy, Suspense } from "react";
import { useLocation } from "react-router-dom";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../components/rbac/RoleGuard";
import { useSupplierOrders } from "../../../hooks/vendor-management/use-supplier-orders";
import { useActiveSuppliersForDropdown } from "../../../hooks/supplier-management/use-suppliers";

// Dynamically import page client for better performance
const SupplierOrdersPageClient = lazy(() => import("./page-client"));

const SupplierOrdersPage = () => {
  const location = useLocation();

  // Parse URL parameters
  const searchParams = new URLSearchParams(location.search);
  const currentPage = parseInt(searchParams.get("page") || "1");
  const pageSize = parseInt(searchParams.get("limit") || "20");
  const searchTerm = searchParams.get("q") || "";
  const status = searchParams.get("status") || "";
  const orderType = searchParams.get("order_type") || "";
  const supplierId = searchParams.get("supplier_id") || "";

  // Build filters for the useSupplierOrders hook
  const filters = useMemo(() => {
    const baseFilters: any = {
      limit: pageSize,
      offset: (currentPage - 1) * pageSize,
    };

    if (searchTerm) baseFilters.search = searchTerm;
    if (status) baseFilters.status = status;
    if (orderType) baseFilters.order_type = orderType;
    if (supplierId) baseFilters.supplier_id = supplierId;

    return baseFilters;
  }, [searchParams, currentPage, pageSize, searchTerm, status, orderType, supplierId]);

  // Fetch supplier orders using the hook
  const {
    data: ordersData,
    isLoading: ordersLoading,
  } = useSupplierOrders(filters);

  // Fetch suppliers for filter dropdown
  const {
    data: suppliersData = [],
    isLoading: suppliersLoading,
  } = useActiveSuppliersForDropdown();

  const orders = ordersData?.orders || [];
  const totalCount = ordersData?.count || 0;
  const isLoading = ordersLoading || suppliersLoading;



  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="supplier_orders:view"
        fallback={
          <Container className="p-6">
            <div className="text-center">
              <Heading level="h2">Access Denied</Heading>
              <Text className="text-ui-fg-subtle mt-2">
                You don't have permission to view supplier orders.
              </Text>
            </div>
          </Container>
        }
      >
        <Suspense
          fallback={
            <Container className="p-6">
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-3 text-sm text-gray-600">Loading orders...</span>
              </div>
            </Container>
          }
        >
          <SupplierOrdersPageClient
            orders={orders}
            suppliers={suppliersData}
            isLoading={isLoading}
            totalCount={totalCount}
            pageSize={pageSize}
          />
        </Suspense>
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Orders",
});

export default SupplierOrdersPage;
