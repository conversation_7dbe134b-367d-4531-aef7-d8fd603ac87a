import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { lazy, Suspense } from "react";
import { Container } from "@camped-ai/ui";
import { MapPin } from "lucide-react";
import { ComponentType } from "react";

// Dynamically import page client for better performance
const PageClient = lazy(() => import("./page-client"));

// Create icon component for route config
const DestinationIcon: ComponentType = (props: any) => (
  <MapPin {...props} className="h-4 w-4" />
);

const DestinationsPage = () => {
  return (
    <Suspense
      fallback={
        <Container className="p-6">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-3 text-sm text-gray-600">Loading carts...</span>
          </div>
        </Container>
      }
    >
      <PageClient />
    </Suspense>
  );
};

export default DestinationsPage;

export const config = defineRouteConfig({
  label: "Destinations",
  icon: DestinationIcon,
});
