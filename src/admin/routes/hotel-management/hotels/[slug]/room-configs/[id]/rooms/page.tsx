import { lazy, Suspense } from "react";
import { Container } from "@camped-ai/ui";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Bed } from "lucide-react";

// Dynamically import page client for better performance
const PageClient = lazy(() => import("./page-client"));

const MainPage = () => {
  return (
    <Suspense
      fallback={
        <Container className="p-6">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-3 text-sm text-gray-600">Loading Hotel...</span>
          </div>
        </Container>
      }
    >
      <PageClient />
    </Suspense>
  );
};

export const config = defineRouteConfig({
  label: "Room Configuration Rooms",
  icon: Bed,
});

export default MainPage;
