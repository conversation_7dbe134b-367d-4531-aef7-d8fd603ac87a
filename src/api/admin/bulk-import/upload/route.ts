import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { DESTINATION_MODULE } from "../../../../modules/hotel-management/destination";
import DestinationModuleService from "../../../../modules/hotel-management/destination/service";
import { HOTEL_MODULE } from "../../../../modules/hotel-management/hotel";
import HotelModuleService from "../../../../modules/hotel-management/hotel/service";
import { IProductModuleService } from "@camped-ai/framework/types";
import * as ExcelJS from "exceljs";
import multer from "multer";
import { z } from "zod";
import {
  isSalesforceFormat,
  mapSalesforceDestinationHeaders,
  mapSalesforceHotelHeaders,
  mapSalesforceRoomHeaders,
  transformSalesforceValues,
  createRoomIdToNameMap,
} from "../../../../utils/salesforce-header-mapping";
import { CreateDestinationWorkflow } from "../../../../workflows/hotel-management/destination/create-destination";
import { CreateHotelWorkflow } from "../../../../workflows/hotel-management/hotel/create-hotel";
import {
  createRoomVariant,
  type SharedRoomData,
  type RoomCreationServices,
} from "../../../../utils/room-import-shared";
import { lookupRoomIdsByRoomNumbers } from "../../hotel-management/rooms/utils/room-lookup";
import linkPriceSetToRoomConfigWorkflow from "../../../../workflows/hotel-management/room/link-price-set-to-room-config";

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (_req: any, file: any, cb: any) => {
    if (
      file.mimetype ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      file.mimetype === "application/vnd.ms-excel"
    ) {
      cb(null, true);
    } else {
      cb(new Error("Only Excel (.xlsx, .xls) files are allowed"));
    }
  },
});

// Validation schemas
const DestinationSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? "" : val)),
  is_active: z
    .union([
      z.boolean(),
      z.string().transform((val) => val?.toLowerCase() === "true"),
      z.null(),
    ])
    .optional()
    .default(true),
  country: z.string().min(1, "Country is required"),
  location: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? "" : val)),
  is_featured: z
    .union([
      z.boolean(),
      z.string().transform((val) => val?.toLowerCase() === "true"),
      z.null(),
    ])
    .optional()
    .default(false),
  tags: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => {
      if (!val || val === null) return [];
      if (typeof val === "string") {
        return val
          .split(",")
          .map((tag) => tag.trim())
          .filter(Boolean);
      }
      return [];
    }),
  website: z
    .union([
      z.string(),
      z.object({}).transform(() => ""),
      z.null(),
      z.undefined(),
    ])
    .optional()
    .transform((val) => val || null),
});

const HotelSchema = z.object({
  name: z.string().min(1, "Name is required"),
  destination: z.string().min(1, "Destination is required"),
  description: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? "" : val)),
  is_active: z
    .union([
      z.boolean(),
      z.string().transform((val) => val?.toLowerCase() === "true"),
      z.null(),
    ])
    .optional()
    .default(true),
  address: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? "" : val)),
  city: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? "" : val)),
  phone_number: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? "" : val)),
  email: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? "" : val)),
  website: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? "" : val)),
  check_in_time: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? "" : val)),
  check_out_time: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? "" : val)),
  rating: z
    .union([
      z.number(),
      z.string().transform((val) => (val ? parseFloat(val) : null)),
      z.null(),
    ])
    .optional(),
  is_pets_allowed: z
    .union([
      z.boolean(),
      z.string().transform((val) => val?.toLowerCase() === "true"),
      z.null(),
    ])
    .optional()
    .default(false),
  amenities: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => {
      if (!val || val === null) return [];
      return val
        .split(",")
        .map((item) => item.trim())
        .filter(Boolean);
    }),
  rules: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => {
      if (!val || val === null) return [];
      return val
        .split(",")
        .map((item) => item.trim())
        .filter(Boolean);
    }),
  safety_measures: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => {
      if (!val || val === null) return [];
      return val
        .split(",")
        .map((item) => item.trim())
        .filter(Boolean);
    }),
  notes: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? "" : val)),
  tags: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => {
      if (!val || val === null) return [];
      return val
        .split(",")
        .map((tag) => tag.trim())
        .filter(Boolean);
    }),
});

const RoomConfigSchema = z.object({
  hotel_name: z.string().min(1, "Hotel name is required"),
  name: z.string().min(1, "Name is required"),
  description: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? "" : val)),
  max_adults: z
    .union([
      z.number(),
      z.string().transform((val) => (val ? parseInt(val, 10) : 1)),
      z.null(),
    ])
    .optional()
    .default(1),
  max_children: z
    .union([
      z.number(),
      z.string().transform((val) => (val ? parseInt(val, 10) : 0)),
      z.null(),
    ])
    .optional()
    .default(0),
  max_infants: z
    .union([
      z.number(),
      z.string().transform((val) => (val ? parseInt(val, 10) : 0)),
      z.null(),
    ])
    .optional()
    .default(0),
  max_extra_beds: z
    .union([
      z.number(),
      z.string().transform((val) => (val ? parseInt(val, 10) : 0)),
      z.null(),
    ])
    .optional()
    .default(0),
  max_extra_cots: z
    .union([
      z.number(),
      z.string().transform((val) => (val ? parseInt(val, 10) : 0)),
      z.null(),
    ])
    .optional()
    .default(0),
  max_adults_beyond_capacity: z
    .union([
      z.number(),
      z.string().transform((val) => (val ? parseInt(val, 10) : 0)),
      z.null(),
    ])
    .optional()
    .default(0),
  bed_types: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => {
      if (!val || val === null) return [];
      return val
        .split(",")
        .map((type) => type.trim())
        .filter(Boolean);
    }),
  room_size: z
    .union([
      z.number(),
      z.string().transform((val) => (val ? parseFloat(val) : null)),
      z.null(),
    ])
    .optional(),
  view_type: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? null : val)),
  amenities: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => {
      if (!val || val === null) return [];
      return val
        .split(",")
        .map((amenity) => amenity.trim())
        .filter(Boolean);
    }),
  tags: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => {
      if (!val || val === null) return [];
      return val
        .split(",")
        .map((tag) => tag.trim())
        .filter(Boolean);
    }),
});

// Bulk import result interface
interface BulkImportResult {
  message: string;
  processing_time: string;
  summary: {
    destinations: { total: number; successful: number; failed: number };
    hotels: { total: number; successful: number; failed: number };
    room_configs: { total: number; successful: number; failed: number };
    rooms: { total: number; successful: number; failed: number };
  };
  results: {
    destinations: {
      successful: number;
      failed: number;
      created: any[];
      errors: any[];
    };
    hotels: {
      successful: number;
      failed: number;
      created: any[];
      errors: any[];
    };
    room_configs: {
      successful: number;
      failed: number;
      created: any[];
      errors: any[];
    };
    rooms: {
      successful: number;
      failed: number;
      created: any[];
      errors: any[];
    };
  };
}

/**
 * Generate a URL-friendly slug from a name
 */
const generateSlugFromName = (name: string): string => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, "") // Remove special characters except spaces
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/-+/g, "-") // Collapse multiple hyphens
    .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
};

/**
 * Generate a URL-friendly slug combining destination name and country
 */
const generateSlugWithCountry = (name: string, country: string): string => {
  const nameSlug = generateSlugFromName(name);
  const countrySlug = generateSlugFromName(country);
  return `${nameSlug}-${countrySlug}`;
};

/**
 * Generate a URL-friendly slug combining hotel handle and room config name
 */
const generateRoomConfigHandle = (
  hotelHandle: string,
  roomConfigName: string
): string => {
  const roomConfigSlug = generateSlugFromName(roomConfigName);
  return `${hotelHandle}-${roomConfigSlug}`;
};

/**
 * POST endpoint to process bulk import from Excel file
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  const multerUpload = upload.single("file");

  multerUpload(req, res, async (err: any) => {
    if (err) {
      return res.status(400).json({ message: err.message });
    }

    if (!(req as any).file) {
      return res.status(400).json({ message: "No file uploaded" });
    }

    const startTime = Date.now();

    try {
      // Get services
      const destinationModuleService: DestinationModuleService =
        req.scope.resolve(DESTINATION_MODULE);
      const hotelModuleService: HotelModuleService =
        req.scope.resolve(HOTEL_MODULE);
      const productModuleService: IProductModuleService = req.scope.resolve(
        Modules.PRODUCT
      );

      // Parse Excel file
      const workbook = new ExcelJS.Workbook();
      await workbook.xlsx.load((req as any).file.buffer);

      // Initialize results
      const results: BulkImportResult = {
        message: "",
        processing_time: "",
        summary: {
          destinations: { total: 0, successful: 0, failed: 0 },
          hotels: { total: 0, successful: 0, failed: 0 },
          room_configs: { total: 0, successful: 0, failed: 0 },
          rooms: { total: 0, successful: 0, failed: 0 },
        },
        results: {
          destinations: { successful: 0, failed: 0, created: [], errors: [] },
          hotels: { successful: 0, failed: 0, created: [], errors: [] },
          room_configs: { successful: 0, failed: 0, created: [], errors: [] },
          rooms: { successful: 0, failed: 0, created: [], errors: [] },
        },
      };

      // Phase 1: Process Destinations
      console.log("🏁 Phase 1: Processing Destinations...");
      const destinationMap = await processDestinations(
        workbook,
        results,
        req.scope
      );

      // Phase 2: Process Hotels
      console.log("🏨 Phase 2: Processing Hotels...");
      const hotelMap = await processHotels(
        workbook,
        results,
        destinationMap,
        req.scope
      );

      // Phase 3: Process Room Configs
      console.log("🛏️ Phase 3: Processing Room Configs...");
      const roomConfigMap = await processRoomConfigs(
        workbook,
        results,
        hotelMap,
        req.scope
      );

      // Phase 4: Process Rooms
      console.log("🏠 Phase 4: Processing Rooms...");
      await processRooms(
        workbook,
        results,
        roomConfigMap,
        hotelMap,
        {
          productModuleService,
          hotelModuleService,
          destinationModuleService,
        },
        req.scope
      );

      // Calculate processing time
      const endTime = Date.now();
      const processingTime = ((endTime - startTime) / 1000).toFixed(2);
      results.processing_time = `${processingTime}s`;

      // Set summary totals
      results.summary.destinations.total =
        results.results.destinations.successful +
        results.results.destinations.failed;
      results.summary.hotels.total =
        results.results.hotels.successful + results.results.hotels.failed;
      results.summary.room_configs.total =
        results.results.room_configs.successful +
        results.results.room_configs.failed;
      results.summary.rooms.total =
        results.results.rooms.successful + results.results.rooms.failed;

      results.summary.destinations.successful =
        results.results.destinations.successful;
      results.summary.destinations.failed = results.results.destinations.failed;
      results.summary.hotels.successful = results.results.hotels.successful;
      results.summary.hotels.failed = results.results.hotels.failed;
      results.summary.room_configs.successful =
        results.results.room_configs.successful;
      results.summary.room_configs.failed = results.results.room_configs.failed;
      results.summary.rooms.successful = results.results.rooms.successful;
      results.summary.rooms.failed = results.results.rooms.failed;

      // Generate summary message
      const totalSuccessful =
        results.summary.destinations.successful +
        results.summary.hotels.successful +
        results.summary.room_configs.successful +
        results.summary.rooms.successful;
      const totalFailed =
        results.summary.destinations.failed +
        results.summary.hotels.failed +
        results.summary.room_configs.failed +
        results.summary.rooms.failed;

      results.message = `Bulk import completed in ${processingTime}s. ${totalSuccessful} entities created successfully, ${totalFailed} failed.`;

      console.log(
        `✅ Bulk import completed: ${totalSuccessful} successful, ${totalFailed} failed`
      );

      res.json(results);
    } catch (error) {
      console.error("Error processing bulk import:", error);
      res.status(500).json({
        message: "Failed to process bulk import",
        error: error.message,
      });
    }
  });
};

/**
 * Process Destinations sheet
 */
async function processDestinations(
  workbook: ExcelJS.Workbook,
  results: BulkImportResult,
  scope: any
): Promise<Map<string, string>> {
  const destinationMap = new Map<string, string>();
  const destinationsSheet = workbook.getWorksheet("Destinations");

  if (!destinationsSheet) {
    console.log("⚠️ No Destinations sheet found");
    return destinationMap;
  }

  // Get headers
  const headers = destinationsSheet.getRow(1).values as string[];

  // Check if this is Salesforce format and use appropriate mapping
  const isSalesforce = isSalesforceFormat(headers);
  console.log(
    `🔍 Detected ${
      isSalesforce ? "Salesforce" : "Standard"
    } format for Destinations`
  );

  let headerMap: any = {};
  if (isSalesforce) {
    headerMap = mapSalesforceDestinationHeaders(headers);
  } else {
    // Use existing header mapping logic
    headers.forEach((header, index) => {
      if (header) {
        let cleanHeader = header.replace("*", "").trim().toLowerCase();

        // Handle comma separated fields like the existing hotel import
        if (cleanHeader.includes("comma separated")) {
          if (cleanHeader.includes("tags")) {
            cleanHeader = "tags";
          }
        } else {
          // For other headers, just clean them up
          cleanHeader = cleanHeader.replace(/[()]/g, "").replace(/\s+/g, "_");
        }

        headerMap[index] = cleanHeader;
      }
    });
  }

  // Process rows
  for (let i = 2; i <= destinationsSheet.rowCount; i++) {
    const row = destinationsSheet.getRow(i);

    // Skip empty rows
    if (Array.isArray(row.values) && row.values.filter(Boolean).length <= 1) {
      continue;
    }

    // Convert row to object
    const rowData: any = {};
    row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
      const header = headerMap[colNumber];
      if (header) {
        rowData[header] = cell.value;
      }
    });

    try {
      // Transform Salesforce data if needed
      const transformedData = isSalesforce
        ? transformSalesforceValues(rowData, "destination")
        : rowData;

      // Debug: Log the data transformation
      if (isSalesforce) {
        console.log("🔍 Destination raw data:", rowData);
        console.log("🔍 Destination transformed data:", transformedData);
      }

      // Validate the data
      const validatedData = DestinationSchema.parse(transformedData);

      // Debug: Log the validated data
      if (isSalesforce) {
        console.log("🔍 Destination validated data:", validatedData);
      }

      if (!validatedData.name || !validatedData.country) {
        throw new Error(
          "Missing required fields: name and country are required"
        );
      }

      // Auto-generate handle from name if not provided
      let handle = generateSlugFromName(validatedData.name);

      // Try to create the destination
      let result: any;
      try {
        const workflowResult = await CreateDestinationWorkflow(scope).run({
          input: {
            name: validatedData.name,
            handle: handle,
            description: validatedData.description || "",
            is_active: validatedData.is_active,
            country: validatedData.country,
            location: validatedData.location || null,
            is_featured: validatedData.is_featured,
            tags: validatedData.tags || [],
            website: validatedData.website || null,
          },
        });
        result = workflowResult.result;
      } catch (workflowError: any) {
        // Check if it's a duplicate handle error
        if (
          workflowError.message &&
          workflowError.message.includes("already exists")
        ) {
          // Retry with country name appended to handle
          const handleWithCountry = generateSlugWithCountry(
            validatedData.name,
            validatedData.country
          );

          const retryWorkflowResult = await CreateDestinationWorkflow(
            scope
          ).run({
            input: {
              name: validatedData.name,
              handle: handleWithCountry,
              description: validatedData.description || "",
              is_active: validatedData.is_active,
              country: validatedData.country,
              location: validatedData.location || null,
              is_featured: validatedData.is_featured,
              tags: validatedData.tags || [],
              website: validatedData.website || null,
            },
          });
          result = retryWorkflowResult.result;
        } else {
          throw workflowError;
        }
      }

      // Store the name to ID mapping
      destinationMap.set(validatedData.name, result.id);

      results.results.destinations.successful++;
      results.results.destinations.created.push({
        id: result.id,
        name: validatedData.name,
        row: i,
      });
    } catch (error: any) {
      results.results.destinations.failed++;
      results.results.destinations.errors.push({
        row: i,
        data: rowData,
        error: error.message || "Unknown error",
      });
    }
  }

  console.log(
    `✅ Destinations processed: ${results.results.destinations.successful} successful, ${results.results.destinations.failed} failed`
  );
  return destinationMap;
}

/**
 * Process Hotels sheet
 */
async function processHotels(
  workbook: ExcelJS.Workbook,
  results: BulkImportResult,
  destinationMap: Map<string, string>,
  scope: any
): Promise<Map<string, string>> {
  const hotelMap = new Map<string, string>();
  const hotelsSheet = workbook.getWorksheet("Hotels");

  if (!hotelsSheet) {
    console.log("⚠️ No Hotels sheet found");
    return hotelMap;
  }

  // Get headers
  const headers = hotelsSheet.getRow(1).values as string[];

  // Check if this is Salesforce format and use appropriate mapping
  const isSalesforce = isSalesforceFormat(headers);
  console.log(
    `🔍 Detected ${isSalesforce ? "Salesforce" : "Standard"} format for Hotels`
  );

  let headerMap: any = {};
  if (isSalesforce) {
    headerMap = mapSalesforceHotelHeaders(headers);
  } else {
    // Use existing header mapping logic
    headers.forEach((header, index) => {
      if (header) {
        let cleanHeader = header.replace("*", "").trim().toLowerCase();

        // Handle comma separated fields like the existing hotel import
        if (cleanHeader.includes("comma separated")) {
          if (cleanHeader.includes("amenities")) {
            cleanHeader = "amenities";
          } else if (cleanHeader.includes("rules")) {
            cleanHeader = "rules";
          } else if (cleanHeader.includes("tags")) {
            cleanHeader = "tags";
          } else if (cleanHeader.includes("safety measures")) {
            cleanHeader = "safety_measures";
          }
        } else if (cleanHeader.includes("safety measures")) {
          cleanHeader = "safety_measures";
        } else if (cleanHeader.includes("pets allowed")) {
          cleanHeader = "is_pets_allowed";
        } else {
          // For other headers, just clean them up
          cleanHeader = cleanHeader.replace(/[()]/g, "").replace(/\s+/g, "_");
        }

        headerMap[index] = cleanHeader;
      }
    });
  }

  // Process rows
  for (let i = 2; i <= hotelsSheet.rowCount; i++) {
    const row = hotelsSheet.getRow(i);

    // Skip empty rows
    if (Array.isArray(row.values) && row.values.filter(Boolean).length <= 1) {
      continue;
    }

    // Convert row to object
    const rowData: any = {};
    row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
      const header = headerMap[colNumber];
      if (header) {
        rowData[header] = cell.value;
      }
    });

    try {
      // Transform Salesforce data if needed
      const transformedData = isSalesforce
        ? transformSalesforceValues(rowData, "hotel")
        : rowData;

      // Debug: Log the data transformation
      if (isSalesforce) {
        console.log("🔍 Hotel raw data:", rowData);
        console.log("🔍 Hotel transformed data:", transformedData);
      }

      // Validate the data
      const validatedData = HotelSchema.parse(transformedData);

      // Debug: Log the validated data
      if (isSalesforce) {
        console.log("🔍 Hotel validated data:", validatedData);
      }

      if (!validatedData.name || !validatedData.destination) {
        throw new Error(
          "Missing required fields: name and destination are required"
        );
      }

      // Look up destination ID
      const destinationId = destinationMap.get(validatedData.destination);
      if (!destinationId) {
        throw new Error(
          `Destination '${validatedData.destination}' not found. Make sure it exists in the Destinations sheet.`
        );
      }

      // Auto-generate handle from name
      let handle = generateSlugFromName(validatedData.name);

      // Try to create the hotel
      let result: any;
      try {
        const workflowResult = await CreateHotelWorkflow(scope).run({
          input: {
            name: validatedData.name,
            handle: handle,
            destination_id: destinationId,
            description: validatedData.description || "",
            is_active: validatedData.is_active,
            address: validatedData.address || null,
            location: validatedData.city || null,
            phone_number: validatedData.phone_number || null,
            email: validatedData.email || null,
            website: validatedData.website || null,
            check_in_time: validatedData.check_in_time || null,
            check_out_time: validatedData.check_out_time || null,
            rating: validatedData.rating || null,
            is_pets_allowed: validatedData.is_pets_allowed || false,
            amenities: validatedData.amenities || [],
            rules: validatedData.rules || [],
            safety_measures: validatedData.safety_measures || [],
            notes: validatedData.notes || null,
            tags: validatedData.tags || [],
          },
        });
        result = workflowResult.result;
      } catch (workflowError: any) {
        // Check if it's a duplicate handle error
        if (
          workflowError.message &&
          workflowError.message.includes("already exists")
        ) {
          // Retry with destination name appended to handle
          const handleWithDestination = `${handle}-${generateSlugFromName(
            validatedData.destination
          )}`;

          const retryWorkflowResult = await CreateHotelWorkflow(scope).run({
            input: {
              name: validatedData.name,
              handle: handleWithDestination,
              destination_id: destinationId,
              description: validatedData.description || "",
              is_active: validatedData.is_active,
              address: validatedData.address || null,
              location: validatedData.city || null,
              phone_number: validatedData.phone_number || null,
              email: validatedData.email || null,
              website: validatedData.website || null,
              check_in_time: validatedData.check_in_time || null,
              check_out_time: validatedData.check_out_time || null,
              rating: validatedData.rating || null,
              is_pets_allowed: validatedData.is_pets_allowed || false,
              amenities: validatedData.amenities || [],
              rules: validatedData.rules || [],
              safety_measures: validatedData.safety_measures || [],
              notes: validatedData.notes || null,
              tags: validatedData.tags || [],
            },
          });
          result = retryWorkflowResult.result;
        } else {
          throw workflowError;
        }
      }

      // Store the name to ID mapping
      hotelMap.set(validatedData.name, result.id);

      results.results.hotels.successful++;
      results.results.hotels.created.push({
        id: result.id,
        name: validatedData.name,
        destination: validatedData.destination,
        row: i,
      });
    } catch (error: any) {
      results.results.hotels.failed++;
      results.results.hotels.errors.push({
        row: i,
        data: rowData,
        error: error.message || "Unknown error",
      });
    }
  }

  console.log(
    `✅ Hotels processed: ${results.results.hotels.successful} successful, ${results.results.hotels.failed} failed`
  );
  return hotelMap;
}

/**
 * Process Room Configs sheet
 */
async function processRoomConfigs(
  workbook: ExcelJS.Workbook,
  results: BulkImportResult,
  hotelMap: Map<string, string>,
  scope: any
): Promise<Map<string, string>> {
  const roomConfigMap = new Map<string, string>();
  const roomConfigsSheet = workbook.getWorksheet("RoomConfigs");

  if (!roomConfigsSheet) {
    console.log("⚠️ No RoomConfigs sheet found");
    return roomConfigMap;
  }

  // Get headers
  const headers = roomConfigsSheet.getRow(1).values as string[];
  const headerMap: any = {};
  headers.forEach((header, index) => {
    if (header) {
      let cleanHeader = header.replace("*", "").trim().toLowerCase();

      // Handle comma separated fields like the existing room config import
      if (cleanHeader.includes("comma separated")) {
        if (cleanHeader.includes("bed types")) {
          cleanHeader = "bed_types";
        } else if (cleanHeader.includes("amenities")) {
          cleanHeader = "amenities";
        } else if (cleanHeader.includes("tags")) {
          cleanHeader = "tags";
        }
      } else {
        // For other headers, just clean them up
        cleanHeader = cleanHeader.replace(/[()]/g, "").replace(/\s+/g, "_");
      }

      headerMap[index] = cleanHeader;
    }
  });

  // Process rows
  for (let i = 2; i <= roomConfigsSheet.rowCount; i++) {
    const row = roomConfigsSheet.getRow(i);

    // Skip empty rows
    if (Array.isArray(row.values) && row.values.filter(Boolean).length <= 1) {
      continue;
    }

    // Convert row to object
    const rowData: any = {};
    row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
      const header = headerMap[colNumber];
      if (header) {
        rowData[header] = cell.value;
      }
    });

    try {
      // Validate the data
      const validatedData = RoomConfigSchema.parse(rowData);

      if (!validatedData.hotel_name || !validatedData.name) {
        throw new Error(
          "Missing required fields: hotel_name and name are required"
        );
      }

      // Look up hotel ID
      const hotelId = hotelMap.get(validatedData.hotel_name);
      if (!hotelId) {
        throw new Error(
          `Hotel '${validatedData.hotel_name}' not found. Make sure it exists in the Hotels sheet.`
        );
      }

      // Get hotel details for handle generation
      const hotelModuleService: HotelModuleService =
        scope.resolve(HOTEL_MODULE);
      const hotel = await hotelModuleService.retrieveHotel(hotelId);

      // Auto-generate handle from hotel handle + room config name
      const handle = generateRoomConfigHandle(hotel.handle, validatedData.name);

      // Get product module service
      const productModuleService = scope.resolve(Modules.PRODUCT);

      // Prepare room config metadata
      const metadata = {
        type: "standard",
        room_size: validatedData.room_size || "",
        bed_type: validatedData.bed_types
          ? validatedData.bed_types.join(",")
          : "",
        max_extra_beds: validatedData.max_extra_beds || 0,
        max_extra_cots: validatedData.max_extra_cots || 0,
        max_adults: validatedData.max_adults || 1,
        max_adults_beyond_capacity:
          validatedData.max_adults_beyond_capacity || 0,
        max_children: validatedData.max_children || 0,
        max_infants: validatedData.max_infants || 0,
        max_occupancy: validatedData.max_adults || 1,
        amenities: validatedData.amenities || [],
        hotel_id: String(hotelId),
        view_type: validatedData.view_type || null,
        tags: validatedData.tags || [],
      };

      // Create room config product data
      const productData = {
        title: validatedData.name,
        handle: handle,
        description: validatedData.description || "",
        is_giftcard: false,
        status: "published" as const,
        metadata,
      };

      // Create the room config product
      const result = await productModuleService.createProducts(productData);

      // Automatically create and link a price set for the new room configuration
      let priceSetId = null;
      try {
        console.log(`Creating price set for room configuration: ${result.id}`);
        const { result: priceSetResult } =
          await linkPriceSetToRoomConfigWorkflow(scope).run({
            input: {
              room_config_id: result.id,
              currency_code: "GBP", // Default currency
              base_price: 10000, // Default $100.00
            },
          });
        priceSetId = priceSetResult.price_set_id;
        console.log(`Successfully created and linked price set: ${priceSetId}`);
      } catch (error: any) {
        console.warn(
          `Failed to create price set for room config ${result.id}:`,
          error
        );
        // Don't fail the room config creation if price set creation fails
      }

      // Store the name to ID mapping (use hotel_name + room_config_name as key for uniqueness)
      const roomConfigKey = `${validatedData.hotel_name}|${validatedData.name}`;
      roomConfigMap.set(roomConfigKey, result.id);

      results.results.room_configs.successful++;
      results.results.room_configs.created.push({
        id: result.id,
        name: validatedData.name,
        hotel_name: validatedData.hotel_name,
        handle: handle,
        price_set_id: priceSetId, // Include the price set ID in the response
        row: i,
      });
    } catch (error: any) {
      results.results.room_configs.failed++;
      results.results.room_configs.errors.push({
        row: i,
        data: rowData,
        error: error.message || "Unknown error",
      });
    }
  }

  console.log(
    `✅ Room Configs processed: ${results.results.room_configs.successful} successful, ${results.results.room_configs.failed} failed`
  );
  return roomConfigMap;
}

/**
 * Process Rooms sheet
 */
async function processRooms(
  workbook: ExcelJS.Workbook,
  results: BulkImportResult,
  roomConfigMap: Map<string, string>,
  hotelMap: Map<string, string>,
  services: any,
  scope: any
): Promise<void> {
  const roomsSheet = workbook.getWorksheet("Rooms");

  if (!roomsSheet) {
    console.log("⚠️ No Rooms sheet found");
    return;
  }

  // Get headers
  const headers = roomsSheet.getRow(1).values as string[];

  // Check if this is Salesforce format and use appropriate mapping
  const isSalesforce = isSalesforceFormat(headers);
  console.log(
    `🔍 Detected ${isSalesforce ? "Salesforce" : "Standard"} format for Rooms`
  );

  let headerMap: any = {};
  if (isSalesforce) {
    headerMap = mapSalesforceRoomHeaders(headers);
  } else {
    // Use existing header mapping logic
    headers.forEach((header, index) => {
      if (header) {
        const cleanHeader = header
          .replace("*", "")
          .trim()
          .toLowerCase()
          .replace(/\s+/g, "_");
        headerMap[index] = cleanHeader;
      }
    });
  }

  // Create room ID to name mapping for Salesforce connected room lookup
  let roomIdToNameMap: Map<string, string> | undefined;
  if (isSalesforce) {
    // First pass: collect all room data to create ID to name mapping
    const allRoomsData: any[] = [];
    for (let i = 2; i <= roomsSheet.rowCount; i++) {
      const row = roomsSheet.getRow(i);
      if (Array.isArray(row.values) && row.values.filter(Boolean).length <= 1) {
        continue;
      }

      const rowData: any = {};
      row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const header = headerMap[colNumber];
        if (header) {
          rowData[header] = cell.value;
        }
      });

      allRoomsData.push(rowData);
    }

    roomIdToNameMap = createRoomIdToNameMap(allRoomsData);
  }

  // Phase 1: Create all rooms without relationships
  const createdRooms = new Map<string, any>(); // room_number -> room data

  for (let i = 2; i <= roomsSheet.rowCount; i++) {
    const row = roomsSheet.getRow(i);

    // Skip empty rows
    if (Array.isArray(row.values) && row.values.filter(Boolean).length <= 1) {
      continue;
    }

    // Convert row to object
    const rowData: any = {};
    row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
      const header = headerMap[colNumber];
      if (header) {
        rowData[header] = cell.value;
      }
    });

    // Transform Salesforce data if needed
    const transformedData = isSalesforce
      ? transformSalesforceValues(rowData, "room", roomIdToNameMap)
      : rowData;

    try {
      if (
        !transformedData.hotel_name ||
        !transformedData.room_config_name ||
        !transformedData.room_number
      ) {
        throw new Error(
          "Missing required fields: hotel_name, room_config_name, and room_number are required"
        );
      }

      // Look up hotel ID
      const hotelId = hotelMap.get(transformedData.hotel_name);
      if (!hotelId) {
        throw new Error(
          `Hotel '${transformedData.hotel_name}' not found. Make sure it exists in the Hotels sheet.`
        );
      }

      // Look up room config ID
      const roomConfigKey = `${transformedData.hotel_name}|${transformedData.room_config_name}`;
      let roomConfigId = roomConfigMap.get(roomConfigKey);

      // Auto-create room config if it doesn't exist (for Salesforce imports)
      if (!roomConfigId && isSalesforce) {
        console.log(
          `🔧 Auto-creating room config '${transformedData.room_config_name}' for hotel '${transformedData.hotel_name}'`
        );

        try {
          // Create room config as a product
          const hotel = await services.hotelModuleService.retrieveHotel(
            hotelId
          );
          const roomConfigHandle = generateRoomConfigHandle(
            hotel.handle,
            transformedData.room_config_name
          );

          const roomConfigProduct =
            await services.productModuleService.createProducts({
              title: transformedData.room_config_name,
              handle: roomConfigHandle,
              is_giftcard: false,
              status: "published",
              metadata: {
                hotel_id: hotelId,
                hotel_name: transformedData.hotel_name,
                room_config_name: transformedData.room_config_name,
                description: `Auto-created room configuration for ${transformedData.room_config_name}`,
                max_adults: 2, // Default values for auto-created configs
                max_children: 0,
                max_infants: 0,
                max_extra_beds: 0,
                max_extra_cots: 0,
                max_adults_beyond_capacity: 0,
                room_size: 0,
                view_type: "",
                bed_types: "",
                amenities: "",
                tags: "",
              },
            });

          roomConfigId = roomConfigProduct.id;
          roomConfigMap.set(roomConfigKey, roomConfigId);

          // Automatically create and link a price set for the auto-created room configuration
          let priceSetId = null;
          try {
            console.log(
              `Creating price set for auto-created room configuration: ${roomConfigId}`
            );
            const { result: priceSetResult } =
              await linkPriceSetToRoomConfigWorkflow(scope).run({
                input: {
                  room_config_id: roomConfigId,
                  currency_code: "GBP", // Default currency
                  base_price: 10000, // Default $100.00
                },
              });
            priceSetId = priceSetResult.price_set_id;
            console.log(
              `Successfully created and linked price set for auto-created room config: ${priceSetId}`
            );
          } catch (error: any) {
            console.warn(
              `Failed to create price set for auto-created room config ${roomConfigId}:`,
              error
            );
            // Don't fail the room config creation if price set creation fails
          }

          console.log(
            `✅ Auto-created room config: ${
              transformedData.room_config_name
            } (ID: ${roomConfigId}) with price set: ${priceSetId || "failed"}`
          );
        } catch (error: any) {
          throw new Error(
            `Failed to auto-create room config '${transformedData.room_config_name}': ${error.message}`
          );
        }
      } else if (!roomConfigId) {
        throw new Error(
          `Room config '${transformedData.room_config_name}' not found for hotel '${transformedData.hotel_name}'. Make sure it exists in the RoomConfigs sheet.`
        );
      }

      // Prepare room data without relationships for Phase 1
      const roomDataWithoutRelationships: SharedRoomData = {
        room_number: transformedData.room_number,
        name: transformedData.name || `Room ${transformedData.room_number}`,
        floor: transformedData.floor || "1",
        notes: transformedData.notes || "",
        status: "available",
        is_active: true,
        left_room: "",
        right_room: "",
        opposite_room: "",
        connected_room: "",
      };

      // Store original relationships for Phase 2
      const originalRelationships = {
        left_room: transformedData.left_room || "",
        right_room: transformedData.right_room || "",
        opposite_room: transformedData.opposite_room || "",
        connected_room: transformedData.connected_room || "",
      };

      // Create room services object
      const roomServices: RoomCreationServices = {
        productModuleService: services.productModuleService,
        hotelId: hotelId,
      };

      // Create the room using shared utilities
      const result = await createRoomVariant(
        roomDataWithoutRelationships,
        roomConfigId,
        roomServices
      );

      if (result.success) {
        // Store created room data for Phase 2 relationship processing
        createdRooms.set(transformedData.room_number, {
          variantId: result.roomId,
          roomNumber: result.roomNumber,
          hotelName: transformedData.hotel_name,
          roomConfigName: transformedData.room_config_name,
          relationships: originalRelationships,
          row: i,
        });

        results.results.rooms.successful++;
        results.results.rooms.created.push({
          id: result.roomId,
          room_number: result.roomNumber,
          hotel_name: transformedData.hotel_name,
          room_config_name: transformedData.room_config_name,
          row: i,
        });
      } else {
        results.results.rooms.failed++;
        results.results.rooms.errors.push({
          row: i,
          data: transformedData,
          error: result.error || "Unknown error creating room",
        });
      }
    } catch (error: any) {
      results.results.rooms.failed++;
      results.results.rooms.errors.push({
        row: i,
        data: transformedData,
        error: error.message || "Unknown error",
      });
    }
  }

  // Phase 2: Update room relationships (convert room numbers to variant IDs)
  if (createdRooms.size > 0) {
    console.log(
      `🔗 Phase 2: Processing relationships for ${createdRooms.size} rooms...`
    );

    try {
      // Group rooms by hotel for efficient processing
      const roomsByHotel = new Map<string, any[]>();
      for (const roomData of createdRooms.values()) {
        const hotelName = roomData.hotelName;
        if (!roomsByHotel.has(hotelName)) {
          roomsByHotel.set(hotelName, []);
        }
        roomsByHotel.get(hotelName)!.push(roomData);
      }

      // Process relationships for each hotel
      for (const [hotelName, hotelRooms] of roomsByHotel) {
        const hotelId = hotelMap.get(hotelName);
        if (!hotelId) continue;

        // Get all room numbers that might be referenced in relationships for this hotel
        const allReferencedRoomNumbers = new Set<string>();
        for (const roomData of hotelRooms) {
          const rels = roomData.relationships;
          if (rels.left_room) allReferencedRoomNumbers.add(rels.left_room);
          if (rels.right_room) allReferencedRoomNumbers.add(rels.right_room);
          if (rels.opposite_room)
            allReferencedRoomNumbers.add(rels.opposite_room);
          if (rels.connected_room)
            allReferencedRoomNumbers.add(rels.connected_room);
        }

        // Add newly created room numbers to the lookup
        for (const roomData of hotelRooms) {
          allReferencedRoomNumbers.add(roomData.roomNumber);
        }

        if (allReferencedRoomNumbers.size === 0) continue;

        // Look up existing room IDs + newly created room IDs
        const roomNumberToIdMap = await lookupRoomIdsByRoomNumbers(
          { resolve: (_service: string) => services.productModuleService },
          hotelId,
          Array.from(allReferencedRoomNumbers)
        );

        // Add newly created rooms to the lookup map
        for (const roomData of hotelRooms) {
          roomNumberToIdMap.set(roomData.roomNumber, roomData.variantId);
        }

        // Update relationships for all created rooms in this hotel
        let relationshipUpdates = 0;

        for (const roomData of hotelRooms) {
          const relationships = roomData.relationships;
          if (!relationships) continue;

          const updates: any = {};
          let hasUpdates = false;

          // Process each relationship type
          if (relationships.left_room) {
            const leftRoomId = roomNumberToIdMap.get(relationships.left_room);
            if (leftRoomId) {
              updates.left_room = leftRoomId;
              hasUpdates = true;
            }
          }

          if (relationships.right_room) {
            const rightRoomId = roomNumberToIdMap.get(relationships.right_room);
            if (rightRoomId) {
              updates.right_room = rightRoomId;
              hasUpdates = true;
            }
          }

          if (relationships.opposite_room) {
            const oppositeRoomId = roomNumberToIdMap.get(
              relationships.opposite_room
            );
            if (oppositeRoomId) {
              updates.opposite_room = oppositeRoomId;
              hasUpdates = true;
            }
          }

          if (relationships.connected_room) {
            const connectedRoomId = roomNumberToIdMap.get(
              relationships.connected_room
            );
            if (connectedRoomId) {
              updates.connected_room = connectedRoomId;
              hasUpdates = true;
            }
          }

          // Update the room variant metadata if there are relationship updates
          if (hasUpdates) {
            try {
              // Get the current variant to preserve existing metadata
              const currentVariant =
                await services.productModuleService.retrieveProductVariant(
                  roomData.variantId
                );

              await services.productModuleService.updateProductVariants(
                roomData.variantId,
                {
                  metadata: {
                    ...currentVariant.metadata, // Preserve ALL existing metadata
                    ...updates, // Add relationship updates
                  },
                }
              );
              relationshipUpdates++;
            } catch (updateError: any) {
              console.error(
                `❌ Failed to update relationships for room ${roomData.roomNumber}:`,
                updateError
              );
            }
          }
        }

        console.log(
          `✅ Hotel ${hotelName}: ${relationshipUpdates} rooms updated with relationships`
        );
      }

      console.log(`✅ Phase 2 completed: Room relationships processed`);
    } catch (error: any) {
      console.error("❌ Error processing room relationships:", error);
      // Don't fail the entire import if relationship processing fails
    }
  }

  console.log(
    `✅ Rooms processed: ${results.results.rooms.successful} successful, ${results.results.rooms.failed} failed`
  );
}
