import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Mo<PERSON>les, ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { DESTINATION_MODULE } from "../../../../modules/hotel-management/destination";
import DestinationModuleService from "../../../../modules/hotel-management/destination/service";
import * as ExcelJS from "exceljs";
import { CreateDestinationWorkflow } from "../../../../workflows/hotel-management/destination/create-destination";
import { IProductModuleService } from "@camped-ai/framework/types";
import multer from "multer";
import { z } from "zod";
import {
  isSalesforceFormat,
  mapSalesforceDestinationHeaders,
  transformSalesforceValues,
} from "../../../../utils/salesforce-header-mapping";

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept Excel and CSV files
    if (
      file.mimetype ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      file.mimetype === "application/vnd.ms-excel" ||
      file.mimetype === "text/csv" ||
      file.mimetype === "application/csv"
    ) {
      cb(null, true);
    } else {
      cb(new Error("Only Excel (.xlsx, .xls) and CSV files are allowed"));
    }
  },
});

// Validation schema for destination data
const DestinationSchema = z.object({
  name: z.string().min(1, "Name is required"),
  handle: z.string().optional(), // Handle is now optional - will be auto-generated from name if not provided
  description: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? "" : val)),
  is_active: z
    .union([
      z.boolean(),
      z.string().transform((val) => val?.toLowerCase() === "true"),
      z.null(),
    ])
    .optional()
    .default(true),
  country: z.string().min(1, "Country is required"),
  location: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => (val === null ? "" : val)),
  is_featured: z
    .union([
      z.boolean(),
      z.string().transform((val) => val?.toLowerCase() === "true"),
      z.null(),
    ])
    .optional()
    .default(false),
  tags: z
    .union([z.string(), z.null()])
    .optional()
    .transform((val) => {
      if (val === null || val === undefined) return [];
      if (typeof val !== "string") return [];

      // Handle JSON array format like "[\"test\",\"one\",\"two\"]"
      if (val.trim().startsWith("[") && val.trim().endsWith("]")) {
        try {
          const parsed = JSON.parse(val);
          if (Array.isArray(parsed)) {
            return parsed
              .map((tag) => String(tag).trim())
              .filter((tag) => tag.length > 0);
          }
        } catch (error) {
          console.warn("Failed to parse tags as JSON array:", val, error);
          // Fall back to comma-separated parsing
        }
      }

      // Handle comma-separated format like "test,one,two"
      return val
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag.length > 0);
    }),
  website: z
    .union([
      z.string(),
      z.object({}).transform(() => ""), // Handle empty objects
      z.null(),
      z.undefined(),
    ])
    .optional()
    .transform((val) => val || null),
});

// Type for the validated data that matches the workflow input requirements
type ValidatedDestinationData = z.infer<typeof DestinationSchema> & {
  name: string; // Ensure name is required
  handle: string; // Handle will be auto-generated if not provided
  country: string; // Ensure country is required
};

/**
 * Generate a URL-friendly slug from a destination name
 * Example: "Bali, Indonesia" -> "bali-indonesia"
 */
const generateSlugFromName = (name: string): string => {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, "") // Remove special characters except spaces
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/-+/g, "-") // Collapse multiple hyphens
    .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
};

/**
 * Generate a URL-friendly slug combining destination name and country
 * Example: "Bali" + "Indonesia" -> "bali-indonesia"
 */
const generateSlugWithCountry = (name: string, country: string): string => {
  const nameSlug = generateSlugFromName(name);
  const countrySlug = generateSlugFromName(country);
  return `${nameSlug}-${countrySlug}`;
};

/**
 * POST endpoint to import destinations from Excel file
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  // Use multer to handle the file upload
  const multerUpload = upload.single("file");

  multerUpload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ message: err.message });
    }

    if (!req.file) {
      return res.status(400).json({ message: "No file uploaded" });
    }

    try {
      // Get services
      const destinationModuleService: DestinationModuleService =
        req.scope.resolve(DESTINATION_MODULE);
      const productModuleService: IProductModuleService = req.scope.resolve(
        Modules.PRODUCT
      );

      // Determine file type and parse accordingly
      const fileType = req.file.originalname.split(".").pop()?.toLowerCase();
      const workbook = new ExcelJS.Workbook();
      let worksheet;

      try {
        if (fileType === "csv") {
          // For CSV files, we need to convert the buffer to a string first
          const csvString = req.file.buffer.toString("utf8");

          // Create a temporary file to handle the CSV (ExcelJS CSV reader needs a file path)
          const tempFilePath = `/tmp/temp_${Date.now()}.csv`;
          require("fs").writeFileSync(tempFilePath, csvString);

          try {
            // Read the CSV file
            await workbook.csv.readFile(tempFilePath);
            worksheet = workbook.worksheets[0]; // CSV files have only one worksheet

            // Clean up the temporary file
            require("fs").unlinkSync(tempFilePath);
          } catch (csvError) {
            console.error("Error reading CSV:", csvError);

            // Alternative approach: parse CSV manually
            const rows = csvString.split("\n").map((row) => row.split(","));

            if (rows.length > 0) {
              // Create a new worksheet
              worksheet = workbook.addWorksheet("Sheet1");

              // Add rows to the worksheet
              rows.forEach((row) => {
                worksheet.addRow(row);
              });
            } else {
              throw new Error("CSV file is empty or invalid");
            }
          }
        } else {
          // Parse Excel file
          await workbook.xlsx.load(req.file.buffer);

          // Try to find a worksheet named 'Destinations' or use the first one
          worksheet =
            workbook.getWorksheet("Destinations") ||
            workbook.getWorksheet("destinations") ||
            workbook.getWorksheet("Sheet1") ||
            workbook.worksheets[0];
        }
      } catch (error) {
        console.error("Error parsing file:", error);
        return res
          .status(400)
          .json({ message: `Error parsing file: ${error.message}` });
      }

      if (!worksheet) {
        return res
          .status(400)
          .json({ message: "Invalid file: No worksheet found" });
      }

      // Get headers
      const headers = worksheet.getRow(1).values as string[];

      // Check if this is Salesforce format and use appropriate mapping
      const isSalesforce = isSalesforceFormat(headers);
      console.log(
        `🔍 Detected ${
          isSalesforce ? "Salesforce" : "Standard"
        } format for Destinations`
      );

      let headerMap = {};
      if (isSalesforce) {
        headerMap = mapSalesforceDestinationHeaders(headers);
      } else {
        // Use existing header mapping logic
        headers.forEach((header, index) => {
          if (header) {
            // Remove the asterisk from required fields and clean up the header
            let cleanHeader = header.replace("*", "").trim().toLowerCase();

            // Handle special cases for headers with additional text
            if (cleanHeader.includes("tags")) {
              cleanHeader = "tags";
            } else if (cleanHeader.includes("is active")) {
              cleanHeader = "is_active";
            } else if (cleanHeader.includes("is featured")) {
              cleanHeader = "is_featured";
            } else {
              // For other headers, just replace spaces with underscores
              cleanHeader = cleanHeader
                .replace(/\s+/g, "_")
                .replace(/[()]/g, "");
            }

            headerMap[index] = cleanHeader;
          }
        });
      }

      // Process destinations first
      const results = {
        destinations: {
          total: 0,
          successful: 0,
          failed: 0,
          errors: [],
          created: [],
        },
        faqs: {
          total: 0,
          successful: 0,
          failed: 0,
          errors: [],
        },
        warnings: [],
      };

      // Store destination names to IDs mapping for FAQ processing
      const destinationNameMap = new Map();

      // Start from row 2 (skip header)
      for (let i = 2; i <= worksheet.rowCount; i++) {
        const row = worksheet.getRow(i);

        // Skip empty rows
        if (row.values.filter(Boolean).length <= 1) {
          continue;
        }

        results.destinations.total++;

        // Convert row to object using header map
        const rowData = {};
        row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          const header = headerMap[colNumber];
          if (header) {
            // Handle special cases for certain fields
            if (header === "website" && cell.value) {
              // Convert website to string if it's an object (happens with hyperlinks in Excel)
              rowData[header] =
                cell.value.text || cell.value.hyperlink || String(cell.value);
            } else {
              rowData[header] = cell.value;
            }
          }
        });

        // Clean up the data before validation
        Object.keys(rowData).forEach((key) => {
          // Handle null values
          if (rowData[key] === null) {
            // Keep it as null for the validator to handle
          }
          // Convert any objects to strings
          else if (
            rowData[key] &&
            typeof rowData[key] === "object" &&
            !(rowData[key] instanceof Array)
          ) {
            rowData[key] = String(rowData[key]);
          }
          // Handle empty strings for fields that should be null
          else if (
            rowData[key] === "" &&
            ["description", "location", "tags", "website"].includes(key)
          ) {
            rowData[key] = null;
          }
        });

        try {
          // Transform Salesforce data if needed
          const transformedData = isSalesforce
            ? transformSalesforceValues(rowData, "destination")
            : rowData;

          // Validate the data
          const validatedData = DestinationSchema.parse(transformedData);

          // Check if required fields are present
          if (!validatedData.name || !validatedData.country) {
            throw new Error(
              "Missing required fields: name and country are required"
            );
          }

          // Auto-generate handle from name if not provided
          if (!validatedData.handle || validatedData.handle.trim() === "") {
            validatedData.handle = generateSlugFromName(validatedData.name);
          }

          // Check for duplicate handle before creating destination
          const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
          const { data: existingDestinations } = await query.graph({
            entity: "destination",
            filters: {
              handle: validatedData.handle,
            },
            fields: ["id", "name", "handle"],
          });

          if (existingDestinations && existingDestinations.length > 0) {
            throw new Error(
              `Destination with handle "${validatedData.handle}" already exists (existing destination: "${existingDestinations[0].name}")`
            );
          }

          // Try to create the destination using the workflow
          const workflowResult = await CreateDestinationWorkflow(req.scope).run(
            {
              input: {
                name: validatedData.name,
                handle: validatedData.handle,
                description: validatedData.description || "",
                is_active: validatedData.is_active,
                country: validatedData.country,
                location: validatedData.location || null,
                is_featured: validatedData.is_featured,
                tags: validatedData.tags || [],
                website: validatedData.website || null,
              },
            }
          );
          const result = workflowResult.result;

          // Store the name to ID mapping for FAQ processing
          destinationNameMap.set(validatedData.name, result.id);

          results.destinations.successful++;
          results.destinations.created.push({
            id: result.id,
            name: validatedData.name,
            handle: validatedData.handle,
            row: i,
          });
        } catch (error) {
          results.destinations.failed++;
          results.destinations.errors.push({
            row: i,
            data: rowData,
            error: error.message || "Unknown error",
          });
        }
      }

      // Process FAQs sheet if it exists
      const faqsSheet =
        workbook.getWorksheet("FAQs") || workbook.getWorksheet("faqs");
      if (faqsSheet) {
        console.log("Processing FAQs sheet...");

        // Get FAQ headers
        const faqHeaders = faqsSheet.getRow(1).values as string[];
        const faqHeaderMap = {};
        faqHeaders.forEach((header, index) => {
          if (header) {
            const cleanHeader = header
              .replace("*", "")
              .trim()
              .toLowerCase()
              .replace(/\s+/g, "_");
            faqHeaderMap[index] = cleanHeader;
          }
        });

        // Process FAQ rows
        for (let i = 2; i <= faqsSheet.rowCount; i++) {
          const row = faqsSheet.getRow(i);

          // Skip empty rows
          if (
            Array.isArray(row.values) &&
            row.values.filter(Boolean).length <= 1
          ) {
            continue;
          }

          results.faqs.total++;

          // Convert row to object using header map
          const faqData = {};
          row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
            const header = faqHeaderMap[colNumber];
            if (header) {
              faqData[header] = cell.value;
            }
          });

          try {
            // Validate FAQ data
            const destinationName = faqData["destination_name"];
            const question = faqData["question"];
            const answer = faqData["answer"];

            if (!destinationName || !question || !answer) {
              throw new Error(
                "Missing required fields: destination_name, question, and answer are required"
              );
            }

            // Check if destination exists
            const destinationId = destinationNameMap.get(destinationName);
            if (!destinationId) {
              throw new Error(
                `Destination with name '${destinationName}' not found. Make sure it exists in the Destinations sheet.`
              );
            }

            // Create FAQ using the service
            await destinationModuleService.createDestinationFaq({
              question: question.toString().trim(),
              answer: answer.toString().trim(),
              destination_id: destinationId,
            });

            results.faqs.successful++;
          } catch (error) {
            results.faqs.failed++;
            results.faqs.errors.push({
              row: i,
              data: faqData,
              error: error.message || "Unknown error",
            });
          }
        }
      } else {
        results.warnings.push(
          "No FAQs sheet found. Only destinations were imported."
        );
      }

      res.json({
        message: `Import completed. ${results.destinations.successful} destinations created, ${results.faqs.successful} FAQs created.`,
        results,
      });
    } catch (error) {
      console.error("Error processing Excel file:", error);
      res.status(500).json({
        message: "Failed to process Excel file",
        error: error.message,
      });
    }
  });
};
