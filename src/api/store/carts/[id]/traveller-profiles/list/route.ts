import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import CustomerTravellerService from "../../../../../../modules/customer-travellers/service";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cart_id = req.params.id;

    // Use proper Medusa dependency resolution
    const manager = req.scope.resolve(ContainerRegistrationKeys.MANAGER);

    if (!manager) {
      return res
        .status(500)
        .json({ message: "Entity manager not available in request scope" });
    }

    // Medusa standard: keep route thin, use service for data access
    const service = new CustomerTravellerService({ manager });
    const profiles = await service.listByCart(cart_id);

    return res.status(200).json({ cart_id, profiles });
  } catch (error) {
    console.error("Error in traveller profiles list:", error);
    return res.status(500).json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
};
