import type { NextFunction, Request, Response } from "express";
import { EntityManager } from "typeorm";
import CustomerTravellerService from "../../../../../../modules/customer-travellers/service";

export default async function (req: Request, res: Response, next: NextFunction) {
  try {
    const cart_id = req.params.id;
    // Expect authenticated customer id available on request. Adjust accessor if different in your auth middleware.
    const customer_id = (req as any).user?.customer_id || (req as any).user?.id || (req as any).customer_id;

    if (!customer_id) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    // Resolve TypeORM manager from request scope per Medusa v1 convention
    const manager: EntityManager = (req as any).scope?.resolve("manager");
    if (!manager) {
      return res.status(500).json({ message: "Entity manager not available in request scope" });
    }
    const service = new CustomerTravellerService({ manager });

    const profiles = await service.listByCustomer(customer_id);

    // Not selecting in this endpoint; just list saved profiles. Selections should be kept in cart.metadata by client or separate endpoint.
    res.status(200).json({ cart_id, customer_id, profiles });
  } catch (e) {
    next(e);
  }
}
