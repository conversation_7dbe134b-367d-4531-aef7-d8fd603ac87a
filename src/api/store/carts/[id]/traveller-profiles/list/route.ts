import { MedusaRequest, MedusaResponse } from "@camped-ai/framework";
import { EntityManager } from "typeorm";
import CustomerTravellerService from "../../../../../modules/customer-travellers/service";

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  const cart_id = req.params.id;

  // Resolve EntityManager from request scope (support multiple common bindings)
  const manager: EntityManager =
    (req as any).scope?.resolve?.("activeManager") ??
    (req as any).scope?.resolve?.("entityManager") ??
    (req as any).scope?.resolve?.("manager");

  if (!manager) {
    return res
      .status(500)
      .json({ message: "Entity manager not available in request scope" });
  }

  // Medusa standard: keep route thin, use service for data access
  const service = new CustomerTravellerService({ manager });
  const profiles = await service.listByCart(cart_id);

  return res.status(200).json({ cart_id, profiles });
};
