import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import CustomerTravellerService from "../../../../../../../modules/customer-travellers/service";

export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cart_id = req.params.id;
    const traveller_id = req.params.traveller_id;
    const customer_id = (req as any).user?.customer_id || (req as any).user?.id || (req as any).customer_id;

    if (!customer_id) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    // Use proper Medusa dependency resolution
    const manager = req.scope.resolve(ContainerRegistrationKeys.MANAGER);
    if (!manager) {
      return res.status(500).json({ message: "Entity manager not available in request scope" });
    }

    const service = new CustomerTravellerService({ manager });

    await service.delete(customer_id, traveller_id);

    res.status(204).send();
  } catch (error) {
    console.error("Error deleting traveller profile:", error);
    return res.status(500).json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}
