import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import CustomerTravellerService from "../../../../../../modules/customer-travellers/service";
import { CreateTravellerProfileSchema, CreateTravellerProfileReq } from "../schemas";

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const cart_id = req.params.id;
    const customer_id = (req as any).user?.customer_id || (req as any).user?.id || (req as any).customer_id;

    if (!customer_id) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    // Validate request body using schema
    const validationResult = CreateTravellerProfileSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Validation failed",
        errors: validationResult.error.errors
      });
    }

    const body = validationResult.data;

    // Use proper Medusa dependency resolution
    const manager = req.scope.resolve(ContainerRegistrationKeys.MANAGER);
    if (!manager) {
      return res.status(500).json({ message: "Entity manager not available in request scope" });
    }

    const service = new CustomerTravellerService({ manager });

    const created = await service.create({
      customer_id,
      cart_id,
      first_name: body.first_name,
      last_name: body.last_name,
      date_of_birth: body.date_of_birth,
      gender: body.gender,
      relationship: body.relationship as any, // Type assertion for now
      is_primary: body.is_primary,
    });

    res.status(201).json({ cart_id, customer_id, profile: created });
  } catch (error) {
    console.error("Error creating traveller profile:", error);
    return res.status(500).json({
      message: "Internal server error",
      error: error instanceof Error ? error.message : "Unknown error"
    });
  }
}
