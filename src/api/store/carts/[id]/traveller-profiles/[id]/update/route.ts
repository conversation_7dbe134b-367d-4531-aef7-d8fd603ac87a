import type { NextFunction, Request, Response } from "express";
import { EntityManager } from "typeorm";
import CustomerTravellerService from "../../../../../../../modules/customer-travellers/service";

export default async function (req: Request, res: Response, next: NextFunction) {
  try {
    const cart_id = req.params.id;
    const id = req.params.id;
    const customer_id = (req as any).user?.customer_id || (req as any).user?.id || (req as any).customer_id;

    if (!customer_id) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    const body = req.body as any;

    // Basic validation: if explicitly setting non-primary, relationship should be present
    if (body.is_primary === false && !body.relationship) {
      return res.status(400).json({ message: "relationship is required for non-primary travellers" });
    }

    const manager: EntityManager = (req as any).scope?.resolve("manager");
    if (!manager) {
      return res.status(500).json({ message: "Entity manager not available in request scope" });
    }

    const service = new CustomerTravellerService({ manager });

    const updated = await service.update(customer_id, id, {
      id,
      cart_id: body.cart_id,
      first_name: body.first_name,
      last_name: body.last_name,
      date_of_birth: body.date_of_birth,
      gender: body.gender,
      relationship: body.relationship,
      is_primary: body.is_primary,
    });

    res.status(200).json({ cart_id, customer_id, profile: updated });
  } catch (e) {
    next(e);
  }
}
