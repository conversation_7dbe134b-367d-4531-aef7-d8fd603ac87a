import type { NextFunction, Request, Response } from "express";
import { EntityManager } from "typeorm";
import CustomerTravellerService from "../../../../../../../modules/customer-travellers/service";

export default async function (req: Request, res: Response, next: NextFunction) {
  try {
    const cart_id = req.params.id;
    const id = req.params.id;
    const customer_id = (req as any).user?.customer_id || (req as any).user?.id || (req as any).customer_id;

    if (!customer_id) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    const manager: EntityManager = (req as any).scope?.resolve("manager");
    if (!manager) {
      return res.status(500).json({ message: "Entity manager not available in request scope" });
    }

    const service = new CustomerTravellerService({ manager });

    await service.delete(customer_id, id);

    res.status(204).send();
  } catch (e) {
    next(e);
  }
}
