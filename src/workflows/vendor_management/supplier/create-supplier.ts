import {
  StepResponse,
  WorkflowResponse,
  createStep,
  createWorkflow,
} from "@camped-ai/framework/workflows-sdk";
import SupplierModuleService from "src/modules/vendor_management/supplier-service";
import { SUPPLIER_MANAGEMENT_MODULE } from "src/modules/vendor_management";
import { emitEventStep } from "@camped-ai/medusa/core-flows";

export type CreateSupplierStepInput = {
  name: string;
  handle?: string;
  description?: string;
  supplier_type?: "company" | "individual";

  preference?: "Preferred" | "Backup"; // Supplier preference level
  status?: "active" | "inactive";
  verification_status?: "verified" | "unverified" | "in_review";
  override_existing_preferred?: boolean; // Flag to override existing preferred supplier

  // Main Contact Information
  email: string; // Required
  phone?: string;

  // Supplier Contacts
  contacts?: Array<{
    name?: string;
    email?: string;
    is_primary?: boolean;
  }>;

  // Business & Region Information
  region?: string;
  timezone?: string;
  language_preference?: string[];

  // Financial Information
  payment_method?: string;
  payout_terms?: string;
  bank_account_details?: Record<string, any>;

  // Categories
  categories?: string[];

  // Business Details
  business_registration_number?: string;
  website?: string;
  tax_id?: string;
  registration_number?: string;
  default_currency?: string; // Currency code (CHF, EUR, USD, etc.)

  // Address Information
  address?: string;
  metadata?: Record<string, any>;
};

type CreateSupplierWorkflowInput = CreateSupplierStepInput;

export const createSupplierStep = createStep(
  "create-supplier-step",
  async (input: CreateSupplierStepInput, { container }) => {
    const supplierModuleService = container.resolve(SUPPLIER_MANAGEMENT_MODULE);

    console.log(
      "Supplier service methods:",
      Object.getOwnPropertyNames(supplierModuleService)
    );
    console.log(
      "Supplier service prototype methods:",
      Object.getOwnPropertyNames(Object.getPrototypeOf(supplierModuleService))
    );



    // Generate handle from name if not provided
    if (!input.handle) {
      input.handle = input.name
        .toLowerCase()
        .replace(/[^a-z0-9]/g, "-")
        .replace(/-+/g, "-");
    }

    // Use the service method that handles handle generation and validation
    const supplier = await supplierModuleService.createSupplierWithHandle({
      name: input.name,
      handle: input.handle,
      description: input.description,
      supplier_type: input.supplier_type || "company",

      preference: input.preference, // Include preference field
      status: input.status || "active",
      verification_status: input.verification_status || "unverified",

      // Main Contact Information
      email: input.email,
      phone: input.phone,

      // Business & Region Information
      region: input.region,
      timezone: input.timezone,
      language_preference: input.language_preference,

      // Financial Information
      payment_method: input.payment_method,
      payout_terms: input.payout_terms,
      bank_account_details: input.bank_account_details,

      // Categories
      categories: input.categories,

      // Contacts
      contacts: input.contacts,

      // Business Details
      business_registration_number: input.business_registration_number,
      website: input.website,
      tax_id: input.tax_id,
      registration_number: input.registration_number,
      default_currency: input.default_currency,

      // Address Information
      address_line_1: input.address_line_1,
      address_line_2: input.address_line_2,
      city: input.city,
      state: input.state,
      postal_code: input.postal_code,
      country: input.country,

      // Address as JSON (for backward compatibility)
      address: input.address,
      metadata: input.metadata,
    });

    return new StepResponse(supplier, supplier.id);
  },
  async (supplierId: string, { container }) => {
    const supplierModuleService: SupplierModuleService = container.resolve(
      SUPPLIER_MANAGEMENT_MODULE
    );
    // Use the inherited deleteSuppliers method from MedusaService
    await supplierModuleService.deleteSuppliers([supplierId]);
  }
);

export const CreateSupplierWorkflow = createWorkflow(
  "create-supplier",
  (input: CreateSupplierWorkflowInput) => {
    // Create the supplier
    const supplier = createSupplierStep(input);

    // Emit event for supplier creation
    emitEventStep({
      eventName: "supplier.created",
      data: {
        supplier: supplier,
        name: input.name,

        status: input.status || "active",
      },
    });

    return new WorkflowResponse(supplier);
  }
);
